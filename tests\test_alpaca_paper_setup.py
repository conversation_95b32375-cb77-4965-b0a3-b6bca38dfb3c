#!/usr/bin/env python3
"""Test Alpaca Paper Trading setup and proper endpoints."""

import requests
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv("config.env")

def test_alpaca_paper_trading_setup():
    """Test Alpaca Paper Trading API setup and endpoints."""
    
    api_key = os.getenv("ALPACA_API_KEY")
    secret_key = os.getenv("ALPACA_API_SECRET")
    
    print("🧪 Testing Alpaca Paper Trading Setup")
    print("=" * 60)
    print(f"API Key: {api_key[:8]}...{api_key[-4:]}")
    print(f"Secret: {secret_key[:8]}...{secret_key[-4:]}")
    
    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": api_key,
        "APCA-API-SECRET-KEY": secret_key
    }
    
    # Test 1: Paper Trading Account endpoint
    print(f"\n1. Testing Paper Trading Account Access...")
    try:
        account_url = "https://paper-api.alpaca.markets/v2/account"
        response = requests.get(account_url, headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Paper account access successful!")
            print(f"   Account ID: {data.get('id', 'N/A')}")
            print(f"   Status: {data.get('status', 'N/A')}")
            print(f"   Trading Blocked: {data.get('trading_blocked', 'N/A')}")
            print(f"   Pattern Day Trader: {data.get('pattern_day_trader', 'N/A')}")
        else:
            print(f"   ❌ Paper account access failed: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ Account test error: {e}")
    
    # Test 2: Paper Trading Assets endpoint (to check general API access)
    print(f"\n2. Testing Paper Trading Assets Access...")
    try:
        assets_url = "https://paper-api.alpaca.markets/v2/assets"
        params = {"status": "active", "asset_class": "us_equity"}
        response = requests.get(assets_url, headers=headers, params=params, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Assets access successful! Found {len(data)} assets")
            if data:
                print(f"   Sample asset: {data[0].get('symbol', 'N/A')}")
        else:
            print(f"   ❌ Assets access failed: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ Assets test error: {e}")
    
    # Test 3: Market Data API (different from trading API)
    print(f"\n3. Testing Market Data API Access...")
    market_data_endpoints = [
        ("Live Market Data", "https://data.alpaca.markets/v2/stocks/AAPL/quotes/latest"),
        ("Paper Market Data", "https://paper-api.alpaca.markets/v2/stocks/AAPL/quotes/latest"),
        ("Sandbox Market Data", "https://data.sandbox.alpaca.markets/v2/stocks/AAPL/quotes/latest")
    ]
    
    for name, url in market_data_endpoints:
        try:
            print(f"   Testing {name}...")
            response = requests.get(url, headers=headers, timeout=10)
            print(f"      Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"      ✅ {name} works!")
                break
            elif response.status_code == 403:
                print(f"      ❌ 403 Forbidden - No access to {name}")
            else:
                print(f"      ❌ {response.status_code} - {name} failed")
        except Exception as e:
            print(f"      ❌ {name} error: {e}")
    
    # Test 4: Options endpoints (the main issue)
    print(f"\n4. Testing Options Endpoints...")
    options_endpoints = [
        ("Live Options", "https://data.alpaca.markets/v1beta1/options/snapshots/AAPL"),
        ("Paper Options", "https://paper-api.alpaca.markets/v1beta1/options/snapshots/AAPL"),
        ("Sandbox Options", "https://data.sandbox.alpaca.markets/v1beta1/options/snapshots/AAPL")
    ]
    
    for name, url in options_endpoints:
        try:
            print(f"   Testing {name}...")
            response = requests.get(url, headers=headers, timeout=10)
            print(f"      Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"      ✅ {name} works! Found options data")
                if "snapshots" in data:
                    print(f"      Options count: {len(data['snapshots'])}")
                break
            elif response.status_code == 403:
                print(f"      ❌ 403 Forbidden - No access to {name}")
            elif response.status_code == 404:
                print(f"      ❌ 404 Not Found - {name} endpoint doesn't exist")
            else:
                print(f"      ❌ {response.status_code} - {name} failed")
                print(f"      Response: {response.text[:100]}")
        except Exception as e:
            print(f"      ❌ {name} error: {e}")
    
    # Test 5: Check if we need different subscription or account type
    print(f"\n5. Account Requirements Check...")
    print(f"   📋 For options data access, you may need:")
    print(f"   • Live trading account (not just paper)")
    print(f"   • Options trading approval")
    print(f"   • Market data subscription")
    print(f"   • Specific API permissions")
    
    print(f"\n6. Next Steps Recommendations...")
    print(f"   1. ✅ Verify account type: Paper vs Live")
    print(f"   2. ✅ Check options trading approval in Alpaca dashboard")
    print(f"   3. ✅ Verify market data subscription status")
    print(f"   4. ✅ Consider contacting Alpaca support for options API access")

if __name__ == "__main__":
    test_alpaca_paper_trading_setup()
