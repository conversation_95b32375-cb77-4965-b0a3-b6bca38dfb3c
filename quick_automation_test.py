#!/usr/bin/env python3
"""Quick Automation Test

Simple test to verify the automation system is working.
"""
import sys
import os

# Setup paths
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'core'))

def test_automation():
    """Quick test of automation system."""
    print("🧪 **QUICK AUTOMATION TEST**")
    print("=" * 35)
    
    try:
        # Test 1: Import automation engine
        print("1. Testing automation engine import...")
        from automation_engine import AutomationEngine, AutomationMode
        print("   ✅ Automation engine imported successfully")
        
        # Test 2: Create engine instance
        print("2. Creating automation engine...")
        engine = AutomationEngine()
        print("   ✅ Engine created successfully")
        
        # Test 3: Test conservative mode
        print("3. Testing conservative mode...")
        result = engine.start_automation(AutomationMode.CONSERVATIVE)
        if "STARTED" in result:
            print("   ✅ Conservative mode started")
            
            # Test status
            status = engine.get_automation_status()
            print(f"   ✅ Status: {status['mode']} mode, Running: {status['is_running']}")
            
            # Stop automation
            engine.stop_automation()
            print("   ✅ Automation stopped")
        else:
            print(f"   ⚠️ Unexpected result: {result}")
        
        # Test 4: Test control functions
        print("4. Testing control functions...")
        from automation_control import start_automation_conservative, get_automation_status
        print("   ✅ Control functions imported")
        
        # Test status function
        status_text = get_automation_status()
        if "AUTOMATION STATUS" in status_text:
            print("   ✅ Status function working")
        else:
            print(f"   ⚠️ Unexpected status: {status_text[:50]}...")
        
        print("\n🎉 **ALL TESTS PASSED!**")
        print("✅ Your automation system is ready to use!")
        print("\n🚀 **Next Steps:**")
        print("   1. Run: python launch_automation.py")
        print("   2. Or run: python main.py")
        print("   3. Click the '🤖 Automate' button")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n🔧 **Fix:**")
        print("   Make sure automation_engine.py and automation_control.py")
        print("   are in the core/ directory")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n🔧 **Troubleshooting:**")
        print("   1. Check file locations")
        print("   2. Verify Python path")
        print("   3. Try running from main directory")
        return False

if __name__ == "__main__":
    test_automation()
