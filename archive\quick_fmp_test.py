#!/usr/bin/env python3
"""Quick test of FMP API to see what's happening."""

import requests
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_fmp_api():
    """Test FMP API directly."""
    
    api_key = os.getenv("FMP_API_KEY")
    print(f"🔑 API Key: {api_key[:8]}..." if api_key else "❌ No API key found")
    
    if not api_key:
        print("❌ FMP_API_KEY not found in environment")
        return
    
    # Test basic stock quote first
    print("\n1. Testing stock quote...")
    try:
        url = "https://financialmodelingprep.com/api/v3/quote/AAPL"
        params = {"apikey": api_key}
        response = requests.get(url, params=params, timeout=10)
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data:
                print(f"   ✅ AAPL price: ${data[0].get('price', 'N/A')}")
            else:
                print(f"   ⚠️ Empty response")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test options chain
    print("\n2. Testing options chain...")
    try:
        url = "https://financialmodelingprep.com/api/v3/options-chain/AAPL"
        params = {"apikey": api_key}
        response = requests.get(url, params=params, timeout=15)
        
        print(f"   Status: {response.status_code}")
        print(f"   Response length: {len(response.text)} chars")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Data type: {type(data)}")
            if isinstance(data, list):
                print(f"   ✅ Got {len(data)} options")
                if data:
                    print(f"   Sample: {data[0]}")
            elif isinstance(data, dict):
                print(f"   Dict keys: {list(data.keys())}")
            else:
                print(f"   Raw response: {response.text[:200]}")
        else:
            print(f"   ❌ Error: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")

if __name__ == "__main__":
    test_fmp_api()
