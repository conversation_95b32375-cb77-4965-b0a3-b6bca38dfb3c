#!/usr/bin/env python3
"""
Verify TotalRecall Enhanced Desktop Backup

This script verifies that your TotalRecall Enhanced system has been
properly backed up to your desktop.
"""

import os
from pathlib import Path

def verify_desktop_backup():
    """Verify the desktop backup is complete and functional."""
    
    print("🔍 TOTALRECALL ENHANCED - BACKUP VERIFICATION")
    print("=" * 60)
    
    desktop_path = Path(r"C:\Users\<USER>\OneDrive\Desktop")
    
    # Find the most recent backup
    backup_dirs = [d for d in desktop_path.iterdir() 
                   if d.is_dir() and d.name.startswith("TotalRecall_Enhanced_Backup_")]
    
    if not backup_dirs:
        print("❌ No TotalRecall Enhanced backup found on desktop!")
        return False
    
    # Get the most recent backup
    latest_backup = max(backup_dirs, key=lambda x: x.stat().st_mtime)
    print(f"📂 Found backup: {latest_backup.name}")
    
    # Check essential directories
    essential_dirs = [
        "core", "trading", "scanners", "data", "config", 
        "docs", "integrations", "utils", "tests", "archive"
    ]
    
    print(f"\n📁 CHECKING DIRECTORIES:")
    missing_dirs = []
    for dir_name in essential_dirs:
        dir_path = latest_backup / dir_name
        if dir_path.exists():
            file_count = len(list(dir_path.rglob("*.py")))
            print(f"   ✅ {dir_name} ({file_count} Python files)")
        else:
            print(f"   ❌ {dir_name} - MISSING")
            missing_dirs.append(dir_name)
    
    # Check essential files
    essential_files = [
        "main.py", "requirements.txt", ".env", "README.md",
        "COMPLETE_FUNCTION_INVENTORY.md", "TOP_TIER_COMPARISON_ANALYSIS.md",
        "SETUP_INSTRUCTIONS.md", "BACKUP_SUMMARY.md"
    ]
    
    print(f"\n📄 CHECKING FILES:")
    missing_files = []
    for file_name in essential_files:
        file_path = latest_backup / file_name
        if file_path.exists():
            size_kb = file_path.stat().st_size / 1024
            print(f"   ✅ {file_name} ({size_kb:.1f} KB)")
        else:
            print(f"   ❌ {file_name} - MISSING")
            missing_files.append(file_name)
    
    # Check ZIP file
    zip_files = list(desktop_path.glob("TotalRecall_Enhanced_Backup_*.zip"))
    if zip_files:
        latest_zip = max(zip_files, key=lambda x: x.stat().st_mtime)
        zip_size_mb = latest_zip.stat().st_size / (1024 * 1024)
        print(f"\n📦 ZIP ARCHIVE:")
        print(f"   ✅ {latest_zip.name} ({zip_size_mb:.1f} MB)")
    else:
        print(f"\n📦 ZIP ARCHIVE:")
        print(f"   ❌ No ZIP file found")
    
    # Calculate total files
    total_files = len(list(latest_backup.rglob("*")))
    total_py_files = len(list(latest_backup.rglob("*.py")))
    backup_size_mb = sum(f.stat().st_size for f in latest_backup.rglob("*") if f.is_file()) / (1024 * 1024)
    
    print(f"\n📊 BACKUP STATISTICS:")
    print(f"   📁 Total Files: {total_files}")
    print(f"   🐍 Python Files: {total_py_files}")
    print(f"   💾 Total Size: {backup_size_mb:.1f} MB")
    print(f"   📂 Backup Location: {latest_backup}")
    
    # Verify core functionality files
    core_files = [
        "core/chat_core.py",
        "core/direct_mcp_integration.py", 
        "core/unified_order_coordinator.py",
        "main.py"
    ]
    
    print(f"\n🔧 CHECKING CORE FUNCTIONALITY:")
    for core_file in core_files:
        file_path = latest_backup / core_file
        if file_path.exists():
            print(f"   ✅ {core_file}")
        else:
            print(f"   ❌ {core_file} - MISSING")
    
    # Summary
    print(f"\n🎯 VERIFICATION SUMMARY:")
    if not missing_dirs and not missing_files:
        print("   ✅ ALL ESSENTIAL COMPONENTS PRESENT")
        print("   ✅ BACKUP IS COMPLETE AND READY TO USE")
        
        print(f"\n🚀 YOUR TOTALRECALL ENHANCED SYSTEM IS READY!")
        print(f"📍 Location: {latest_backup}")
        print(f"📦 ZIP File: Available for easy transport")
        print(f"📋 Setup Guide: Read SETUP_INSTRUCTIONS.md")
        
        print(f"\n💡 QUICK START:")
        print(f"1. Navigate to: {latest_backup}")
        print(f"2. Install dependencies: pip install -r requirements.txt")
        print(f"3. Configure .env file with your API keys")
        print(f"4. Run: python main.py")
        print(f"5. Test: python test_enhanced_mcp_integration.py")
        
        return True
    else:
        print("   ⚠️ SOME COMPONENTS ARE MISSING")
        if missing_dirs:
            print(f"   Missing directories: {', '.join(missing_dirs)}")
        if missing_files:
            print(f"   Missing files: {', '.join(missing_files)}")
        return False

if __name__ == "__main__":
    success = verify_desktop_backup()
    if success:
        print("\n🎉 Backup verification successful!")
    else:
        print("\n⚠️ Backup verification found issues.")
