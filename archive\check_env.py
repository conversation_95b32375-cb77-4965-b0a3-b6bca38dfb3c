#!/usr/bin/env python3
"""Check what environment variables are being loaded."""

import os
from dotenv import load_dotenv

print("🔍 Environment Variable Check")
print("=" * 50)

# Check before loading .env
print("Before loading config.env:")
print(f"ALPACA_API_KEY: {os.getenv('ALPACA_API_KEY', 'NOT SET')}")
print(f"ALPACA_API_SECRET: {os.getenv('ALPACA_API_SECRET', 'NOT SET')}")

# Load config.env
print(f"\nLoading config.env...")
load_dotenv("config.env")

# Check after loading .env
print(f"\nAfter loading config.env:")
print(f"ALPACA_API_KEY: {os.getenv('ALPACA_API_KEY', 'NOT SET')}")
print(f"ALPACA_API_SECRET: {os.getenv('ALPACA_API_SECRET', 'NOT SET')}")

# Check if there are any other .env files
print(f"\nChecking for other .env files...")
try:
    load_dotenv(".env")
    print(f"After loading .env:")
    print(f"ALPACA_API_KEY: {os.getenv('ALPACA_API_KEY', 'NOT SET')}")
    print(f"ALPACA_API_SECRET: {os.getenv('ALPACA_API_SECRET', 'NOT SET')}")
except:
    print("No .env file found")

# Show all environment variables that start with ALPACA
print(f"\nAll ALPACA environment variables:")
for key, value in os.environ.items():
    if key.startswith('ALPACA'):
        print(f"{key}: {value}")
