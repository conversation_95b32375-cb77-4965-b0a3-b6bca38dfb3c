#!/usr/bin/env python3
"""
Demo Incite AI Style Interface
Quick demonstration of all features
"""
import sys
import os
from pathlib import Path

# Add core directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'core'))

def demo_intent_detection():
    """Demo intent detection system"""
    print("🎯 **INTENT DETECTION DEMO**")
    print("=" * 30)
    
    # Import the intent detection function
    sys.path.insert(0, '.')
    from simple_web_interface import detect_user_intent
    
    test_queries = [
        "What's happening in my system right now?",
        "Upload this Think or Swim chart for analysis",
        "Scan for TTM squeeze opportunities",
        "Is buying AAPL a good idea?",
        "Search for NVDA trades last week",
        "How's my performance today?",
        "Help me understand the interface"
    ]
    
    for query in test_queries:
        intents = detect_user_intent(query)
        print(f"Query: '{query}'")
        print(f"Intents: {', '.join(intents).replace('_', ' ').title()}")
        print()

def demo_simple_search():
    """Demo simple search system"""
    print("🔍 **SIMPLE DEEP SEARCH DEMO**")
    print("=" * 35)
    
    try:
        from simple_deep_search import SimpleDeepSearch
        
        # Create search instance
        search = SimpleDeepSearch("data/demo_search.db")
        
        # Add sample data
        print("📝 Adding sample trading data...")
        
        search.add_trade_decision(
            "AAPL", "BUY",
            "Strong TTM squeeze with A+ grade and bullish momentum",
            {"confidence": 92, "entry_price": 150.50, "target": 158.00}
        )
        
        search.add_scanner_result(
            "NVDA", "A", 88.5,
            "Volume surge with momentum confirmation, semiconductor strength",
            {"timeframe": "15min", "volume_ratio": 1.8}
        )
        
        search.add_chart_analysis(
            "TSLA", "5min",
            "TTM squeeze building, waiting for breakout confirmation",
            {"pattern": "squeeze_formation", "bars": 9}
        )
        
        search.add_ai_insight(
            "market_analysis",
            "Market showing strong momentum with VIX at 18.5, favorable for TTM strategies",
            {"vix": 18.5, "market_regime": "trending"}
        )
        
        print("✅ Sample data added")
        
        # Test searches
        test_searches = [
            "AAPL trade decision",
            "NVDA volume",
            "TTM squeeze",
            "market analysis"
        ]
        
        for query in test_searches:
            print(f"\n🔍 Searching: '{query}'")
            results = search.search(query, limit=2)
            
            for i, result in enumerate(results, 1):
                content_preview = result["content"][:80] + "..." if len(result["content"]) > 80 else result["content"]
                print(f"  {i}. {result['source'].replace('_', ' ').title()}: {content_preview}")
        
        # Show stats
        stats = search.get_stats()
        print(f"\n📊 Search Index Stats:")
        print(f"   Total documents: {stats['total_documents']}")
        print(f"   By source: {stats['by_source']}")
        print(f"   Search type: {stats['search_type']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_chart_analysis():
    """Demo chart analysis features"""
    print("📈 **CHART ANALYSIS DEMO**")
    print("=" * 30)
    
    try:
        from chart_vision_analyzer import ChartVisionAnalyzer
        from PIL import Image
        
        # Create analyzer
        analyzer = ChartVisionAnalyzer()
        
        # Create sample image
        test_image = Image.new('RGB', (800, 600), color='lightblue')
        
        # Analyze
        print("🔍 Analyzing sample chart...")
        analysis = analyzer.analyze_chart(test_image, "DEMO")
        
        # Format response
        response = analyzer.format_analysis_response(analysis)
        print(response)
        
        return True
        
    except Exception as e:
        print(f"❌ Chart analysis error: {e}")
        print("💡 Install PIL/Pillow for chart analysis: pip install pillow")
        return False

def show_interface_features():
    """Show interface features"""
    print("🎨 **INCITE AI STYLE INTERFACE FEATURES**")
    print("=" * 45)
    
    print("🌐 **Professional Design:**")
    print("   • Gradient backgrounds and modern styling")
    print("   • Responsive mobile-friendly layout")
    print("   • Professional color scheme")
    print("   • Smooth animations and transitions")
    print()
    
    print("🧠 **AI Intelligence:**")
    print("   • Intent detection for smart responses")
    print("   • Natural language processing")
    print("   • Context-aware conversations")
    print("   • Real-time system awareness")
    print()
    
    print("📈 **Chart Analysis:**")
    print("   • Upload Think or Swim screenshots")
    print("   • AI vision pattern recognition")
    print("   • TTM squeeze detection")
    print("   • Trade recommendations")
    print()
    
    print("🔍 **Deep Search:**")
    print("   • Search all trading data")
    print("   • Fast SQLite FTS search")
    print("   • Relevance scoring")
    print("   • Source filtering")
    print()
    
    print("📊 **TTM Scanner:**")
    print("   • Real-time opportunity detection")
    print("   • A+ to C grading system")
    print("   • Confidence scoring")
    print("   • Entry/exit recommendations")
    print()
    
    print("💼 **Portfolio Management:**")
    print("   • Real-time position tracking")
    print("   • P&L monitoring")
    print("   • Risk exposure analysis")
    print("   • Performance metrics")

def show_launch_instructions():
    """Show how to launch the interface"""
    print("🚀 **HOW TO LAUNCH THE INTERFACE**")
    print("=" * 40)
    
    print("📋 **Step 1: Install Dependencies**")
    print("   pip install streamlit plotly pandas pillow")
    print()
    
    print("📋 **Step 2: Launch Interface**")
    print("   python launch_incite_interface.py")
    print()
    
    print("📋 **Step 3: Access Interface**")
    print("   🌐 Open: http://localhost:8501")
    print("   📱 Mobile friendly")
    print("   🎨 Professional design")
    print()
    
    print("⚡ **Quick Commands to Try:**")
    print("   • 'What's happening in my system right now?'")
    print("   • 'Is buying AAPL a good idea right now?'")
    print("   • 'Show me the best TTM setups available'")
    print("   • Upload a Think or Swim chart")
    print("   • 'Search for NVDA trades last week'")
    print()
    
    print("🎯 **Interface Tabs:**")
    print("   🧠 AI Chat & Deep Search")
    print("   📈 Chart Analysis & Upload")
    print("   📊 TTM Scanner")
    print("   💼 Portfolio Management")

def main():
    """Run all demos"""
    print("🎭 INCITE AI STYLE INTERFACE DEMO")
    print("=" * 40)
    print()
    
    # Demo intent detection
    demo_intent_detection()
    print()
    
    # Demo simple search
    demo_simple_search()
    print()
    
    # Demo chart analysis
    demo_chart_analysis()
    print()
    
    # Show features
    show_interface_features()
    print()
    
    # Show launch instructions
    show_launch_instructions()
    
    print()
    print("🎉 **DEMO COMPLETE!**")
    print("Your Incite AI style interface is ready to launch!")
    print()
    print("🚀 **NEXT STEP:**")
    print("   python launch_incite_interface.py")
    print()
    print("🏆 **YOU NOW HAVE:**")
    print("   • Professional interface like Incite AI")
    print("   • Chart upload with AI vision analysis")
    print("   • Deep Search through trading data")
    print("   • Intent detection for smart responses")
    print("   • Complete AI consciousness")
    print("   • TTM scanner with confidence grading")
    print()
    print("💎 **BETTER THAN INCITE AI AT A FRACTION OF THE COST!**")

if __name__ == "__main__":
    main()
