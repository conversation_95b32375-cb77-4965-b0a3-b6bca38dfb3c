#!/usr/bin/env python3
"""
Test Add Alert Fix
Verify that the add_alert method handles different input types correctly
"""
import sys
import os

# Add paths
sys.path.insert(0, os.getcwd())
sys.path.insert(0, os.path.join(os.getcwd(), 'gui'))

def test_add_alert_method():
    """Test the add_alert method with different input types"""
    print("🧪 Testing add_alert Method")
    print("-" * 30)
    
    try:
        # Import the interface class
        from tkinter_trading_interface import TradingInterface
        
        # Create interface instance (it creates its own root)
        interface = TradingInterface()
        interface.root.withdraw()  # Hide the window
        
        # Test 1: String input (this was causing the error)
        print("Test 1: String input")
        interface.add_alert("🚀 Real-time monitoring started")
        print("✅ String alert handled successfully")
        
        # Test 2: List of dictionaries (original functionality)
        print("\nTest 2: List of dictionaries")
        opportunities = [
            {
                'symbol': 'AAPL',
                'grade': 'A',
                'confidence': 88.5,
                'entry': 150.00,
                'stop': 145.00,
                'target': 158.00
            }
        ]
        interface.add_alert(opportunities)
        print("✅ Dictionary list alert handled successfully")
        
        # Test 3: List of strings
        print("\nTest 3: List of strings")
        interface.add_alert(["Alert 1", "Alert 2", "Alert 3"])
        print("✅ String list alert handled successfully")
        
        # Test 4: Empty list
        print("\nTest 4: Empty list")
        interface.add_alert([])
        print("✅ Empty list alert handled successfully")
        
        # Test 5: Number input
        print("\nTest 5: Number input")
        interface.add_alert(12345)
        print("✅ Number alert handled successfully")
        
        # Test 6: None input
        print("\nTest 6: None input")
        interface.add_alert(None)
        print("✅ None alert handled successfully")
        
        interface.root.destroy()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    return True

def test_monitoring_toggle_simulation():
    """Simulate the monitoring toggle that was causing the error"""
    print("\n🔄 Testing Monitoring Toggle Simulation")
    print("-" * 40)
    
    try:
        # Import the interface class
        from tkinter_trading_interface import TradingInterface
        
        # Create interface instance
        interface = TradingInterface()
        interface.root.withdraw()  # Hide the window
        
        # Simulate the exact calls that were causing the error
        print("Simulating monitoring start...")
        interface.add_alert("🚀 Real-time monitoring started")
        print("✅ Monitoring start alert successful")
        
        print("Simulating monitoring stop...")
        interface.add_alert("⏹️ Real-time monitoring stopped")
        print("✅ Monitoring stop alert successful")
        
        print("Simulating error alert...")
        interface.add_alert("❌ Error toggling monitoring: test error")
        print("✅ Error alert successful")
        
        interface.root.destroy()
        
    except Exception as e:
        print(f"❌ Monitoring simulation failed: {e}")
        return False
    
    return True

def test_automation_panel_simulation():
    """Simulate the automation panel that was also causing errors"""
    print("\n🤖 Testing Automation Panel Simulation")
    print("-" * 40)
    
    try:
        # Import the interface class
        from tkinter_trading_interface import TradingInterface
        
        # Create interface instance
        interface = TradingInterface()
        interface.root.withdraw()  # Hide the window
        
        # Simulate automation alerts
        print("Simulating automation panel alert...")
        interface.add_alert("🤖 Automation control panel opened")
        print("✅ Automation panel alert successful")
        
        interface.root.destroy()
        
    except Exception as e:
        print(f"❌ Automation simulation failed: {e}")
        return False
    
    return True

def main():
    """Main test function"""
    print("🔧 ADD ALERT FIX TEST")
    print("🖥️  Testing String Indexing Fix in add_alert")
    print("=" * 50)
    
    tests = [
        test_add_alert_method,
        test_monitoring_toggle_simulation,
        test_automation_panel_simulation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n🎯 **TEST RESULTS: {passed}/{total} PASSED**")
    
    if passed == total:
        print("\n🎉 **ALL TESTS PASSED!**")
        print("\n✅ **ADD ALERT ERROR FIXED:**")
        print("   • add_alert handles string inputs properly")
        print("   • add_alert handles list inputs properly")
        print("   • add_alert handles any input type safely")
        print("   • No more 'string indices must be integers' errors")
        
        print("\n🚀 **INTERFACE BUTTONS SHOULD NOW WORK:**")
        print("   • Real-time monitoring toggle works")
        print("   • Automation panel button works")
        print("   • All alert functions stable")
        print("   • No more callback errors")
    else:
        print(f"\n⚠️  **{total - passed} TESTS FAILED**")
        print("   • Some issues may remain")
        print("   • Check error messages above")

if __name__ == "__main__":
    main()
