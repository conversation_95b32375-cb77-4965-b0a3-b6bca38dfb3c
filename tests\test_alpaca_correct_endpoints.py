#!/usr/bin/env python3
"""Test Alpaca options API with the correct endpoints from documentation."""

import requests
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv("config.env")

def test_correct_alpaca_endpoints():
    """Test Alpaca options API with the correct endpoints from official docs."""
    
    api_key = os.getenv("ALPACA_API_KEY")
    secret_key = os.getenv("ALPACA_API_SECRET")
    
    print("🧪 Testing Correct Alpaca Options API Endpoints")
    print("=" * 60)
    print(f"API Key: {api_key[:8]}...{api_key[-4:]}")
    print(f"Secret: {secret_key[:8]}...{secret_key[-4:]}")
    
    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": api_key,
        "APCA-API-SECRET-KEY": secret_key
    }
    
    # Test 1: Option Chain endpoint (all options for AAPL)
    print(f"\n1. Testing Option Chain endpoint...")
    print(f"   URL: https://data.alpaca.markets/v1beta1/options/snapshots/AAPL")
    try:
        url = "https://data.alpaca.markets/v1beta1/options/snapshots/AAPL"
        response = requests.get(url, headers=headers, timeout=15)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ SUCCESS! Got option chain data")
            print(f"   Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            
            if isinstance(data, dict) and "snapshots" in data:
                snapshots = data["snapshots"]
                print(f"   📊 Number of option contracts: {len(snapshots)}")
                
                if snapshots:
                    # Show first few option symbols
                    symbols = list(snapshots.keys())[:5]
                    print(f"   📊 Sample symbols: {symbols}")
                    
                    # Show detailed data for first option
                    if symbols:
                        first_symbol = symbols[0]
                        first_data = snapshots[first_symbol]
                        print(f"\n   📊 Detailed data for {first_symbol}:")
                        print(f"      Latest Trade: {first_data.get('latestTrade', 'N/A')}")
                        print(f"      Latest Quote: {first_data.get('latestQuote', 'N/A')}")
                        print(f"      Greeks: {first_data.get('greeks', 'N/A')}")
                else:
                    print("   📊 No option snapshots found")
            else:
                print(f"   📊 Response structure: {data}")
                
        elif response.status_code == 403:
            print(f"   ❌ 403 Forbidden - Check API credentials or subscription")
            print(f"   Response: {response.text[:200]}")
        else:
            print(f"   ❌ API Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ Request failed: {e}")

    # Test 2: Snapshots endpoint with specific symbols
    print(f"\n2. Testing Snapshots endpoint with specific symbols...")
    print(f"   URL: https://data.alpaca.markets/v1beta1/options/snapshots")
    try:
        url = "https://data.alpaca.markets/v1beta1/options/snapshots"
        # Use some common AAPL option symbols (these might not exist, but let's test the endpoint)
        params = {
            "symbols": "AAPL250117C00200000,AAPL250117P00200000"  # Sample option symbols
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=15)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ SUCCESS! Got snapshots data")
            
            if isinstance(data, dict) and "snapshots" in data:
                snapshots = data["snapshots"]
                print(f"   📊 Number of snapshots: {len(snapshots)}")
                
                for symbol, snapshot in snapshots.items():
                    print(f"   📊 {symbol}:")
                    print(f"      Latest Trade: {snapshot.get('latestTrade', {}).get('p', 'N/A')}")
                    print(f"      Latest Quote: bid={snapshot.get('latestQuote', {}).get('bp', 'N/A')}, ask={snapshot.get('latestQuote', {}).get('ap', 'N/A')}")
                    print(f"      Greeks: IV={snapshot.get('greeks', {}).get('iv', 'N/A')}")
            else:
                print(f"   📊 Response structure: {data}")
                
        elif response.status_code == 403:
            print(f"   ❌ 403 Forbidden - Check API credentials or subscription")
            print(f"   Response: {response.text[:200]}")
        else:
            print(f"   ❌ API Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ Request failed: {e}")

    # Test 3: Try different base URLs to see which works
    print(f"\n3. Testing different base URLs...")
    base_urls = [
        "https://data.alpaca.markets/v1beta1/options/snapshots/AAPL",
        "https://data.sandbox.alpaca.markets/v1beta1/options/snapshots/AAPL",
        "https://paper-api.alpaca.markets/v1beta1/options/snapshots/AAPL"
    ]
    
    for i, url in enumerate(base_urls):
        try:
            print(f"   URL {i+1}: {url.split('//')[1].split('/')[0]}")
            response = requests.get(url, headers=headers, timeout=10)
            print(f"      Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"      ✅ SUCCESS! This endpoint works")
                break
            elif response.status_code == 403:
                print(f"      ❌ 403 Forbidden")
            else:
                print(f"      ❌ {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Error: {e}")

if __name__ == "__main__":
    test_correct_alpaca_endpoints()
