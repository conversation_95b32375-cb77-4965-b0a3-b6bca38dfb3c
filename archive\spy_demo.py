#!/usr/bin/env python3
"""Demo of S&P 500 (SPY) options analysis in beginner-friendly format."""

from beginner_formatter import format_beginner_options_response

def demo_spy_analysis():
    """Show what SPY options analysis looks like in beginner format."""
    
    # Sample SPY analysis result (based on real system output)
    spy_result = {
        "symbol": "SPY",
        "strategy": "Bull Call Spread",
        "max_profit": 2.86,
        "max_loss": 2.14,
        "probability_of_profit": 0.15,  # 15% from real system
        "net_premium": 2.14,
        "breakeven_points": [592.14]
    }
    
    print("🇺🇸 S&P 500 (SPY) Options Analysis")
    print("=" * 50)
    
    formatted_response = format_beginner_options_response(spy_result)
    print(formatted_response)
    
    print("\n" + "=" * 50)
    print("💡 This shows how your $500 investment would work:")
    print("• You'd pay $214 to enter this trade")
    print("• You could make up to $286 profit")
    print("• You have a 15% chance of making money")
    print("• Perfect for beginners with $500!")

if __name__ == "__main__":
    demo_spy_analysis()
