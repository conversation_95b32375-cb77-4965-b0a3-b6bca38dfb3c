#!/usr/bin/env python3
"""Test the chat interface with options functionality."""

from chat_core import chat_gpt

def test_chat_options():
    """Test options functionality through the chat interface."""
    print("🤖 Testing Chat Interface with Options")
    print("=" * 50)
    
    # Test different types of options queries
    test_queries = [
        "What's the best options strategy for AAPL if I'm bullish?",
        "I think TSLA is going down, what options should I buy?",
        "Give me a neutral options strategy for SPY",
        "Scan for the best options opportunities today",
        "What are some good options plays for NVDA?",
        "I want aggressive options strategies for QQQ"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Query: '{query}'")
        print("-" * 40)
        try:
            response = chat_gpt(query)
            print(f"Response: {response}")
        except Exception as e:
            print(f"❌ Error: {e}")
        print()

if __name__ == "__main__":
    test_chat_options()
