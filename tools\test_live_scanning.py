#!/usr/bin/env python3
"""
Test live scanning functionality
"""
import sys
import os
import time
import threading

sys.path.insert(0, os.getcwd())

def test_live_scanning():
    """Test the live scanning with progress updates"""
    print("🚀 TESTING LIVE SCANNING WITH PROGRESS")
    print("=" * 50)
    
    try:
        from scanners.proper_ttm_squeeze_scanner import ProperTTMSqueezeScanner
        
        scanner = ProperTTMSqueezeScanner()
        test_symbols = ['PLTR', 'AAPL', 'MSFT', 'NVDA', 'TSLA', 'GOOGL', 'AMZN', 'META', 'JPM', 'V']
        
        print(f"📊 Testing {len(test_symbols)} symbols with live progress...")
        
        opportunities = []
        
        for i, symbol in enumerate(test_symbols):
            print(f"🔍 Scanning {symbol}... ({i+1}/{len(test_symbols)})")
            
            try:
                result = scanner.scan_symbol(symbol, '15min')
                if result and result['confidence'] >= 0.75:
                    opportunities.append(result)
                    grade = result['grade']
                    conf = result['confidence'] * 100
                    price = result['entry_price']
                    print(f"  ✅ FOUND: Grade {grade} ({conf:.0f}%) - ${price:.2f}")
                else:
                    print(f"  ❌ No opportunity")
                    
            except Exception as e:
                print(f"  ❌ Error: {str(e)[:50]}")
            
            # Small delay to simulate real scanning
            time.sleep(0.2)
        
        print(f"\n✅ SCAN COMPLETE: Found {len(opportunities)} opportunities")
        
        for opp in opportunities:
            symbol = opp['symbol']
            grade = opp['grade']
            conf = opp['confidence'] * 100
            price = opp['entry_price']
            print(f"   📈 {symbol}: Grade {grade} ({conf:.0f}%) - ${price:.2f}")
        
        return opportunities
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return []

def test_threaded_scanning():
    """Test threaded scanning to ensure no blocking"""
    print(f"\n🧵 TESTING THREADED SCANNING")
    print("=" * 35)
    
    results = []
    
    def scan_thread():
        print("🔍 Thread started...")
        opps = test_live_scanning()
        results.extend(opps)
        print("✅ Thread completed!")
    
    # Start scan in thread
    thread = threading.Thread(target=scan_thread, daemon=True)
    thread.start()
    
    # Main thread continues (simulating UI)
    for i in range(10):
        print(f"🖥️ UI still responsive... {i+1}/10")
        time.sleep(1)
    
    # Wait for thread to complete
    thread.join(timeout=30)
    
    print(f"\n✅ Threaded test complete: {len(results)} opportunities found")
    return len(results) > 0

def main():
    """Main test function"""
    print("🚀 LIVE SCANNING TEST SUITE")
    print("=" * 40)
    
    # Test 1: Basic live scanning
    opportunities = test_live_scanning()
    
    # Test 2: Threaded scanning
    threaded_success = test_threaded_scanning()
    
    print(f"\n🎯 TEST RESULTS:")
    print(f"   📊 Opportunities found: {len(opportunities)}")
    print(f"   🧵 Threaded scanning: {'✅ PASS' if threaded_success else '❌ FAIL'}")
    
    if opportunities and threaded_success:
        print(f"\n✅ ALL TESTS PASSED - Live scanning is working!")
        print(f"💡 Your dashboard should now show live progress when you click 'SCAN S&P 500'")
    else:
        print(f"\n❌ SOME TESTS FAILED - Check the issues above")

if __name__ == "__main__":
    main()
