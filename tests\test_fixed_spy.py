#!/usr/bin/env python3
"""Test the fixed SPY options analysis with correct probability."""

from chat_core import get_beginner_options_recommendation

def test_fixed_spy():
    """Test SPY with the corrected dividend yield and probability."""
    
    print("🧪 Testing Fixed SPY Options Analysis")
    print("=" * 50)
    
    result = get_beginner_options_recommendation('SPY', 'bullish', 'moderate')
    print(result)
    
    print("\n" + "=" * 50)
    print("✅ This should now show a realistic probability (50%+)")
    print("📊 Instead of the broken 15% we had before")

if __name__ == "__main__":
    test_fixed_spy()
