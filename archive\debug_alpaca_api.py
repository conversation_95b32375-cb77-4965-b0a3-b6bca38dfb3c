#!/usr/bin/env python3
"""Debug script to test Alpaca Market Data API endpoints directly."""

import requests
from config import get_api_key

def test_alpaca_endpoints():
    """Test various Alpaca Market Data API endpoints."""
    
    api_key = get_api_key("ALPACA_API_KEY")
    api_secret = get_api_key("ALPACA_API_SECRET")
    
    print(f"🔑 Using API Key: {api_key[:8]}...")
    print(f"🔑 Using API Secret: {api_secret[:8]}...")
    
    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": api_key,
        "APCA-API-SECRET-KEY": api_secret
    }
    
    # Test different base URLs
    base_urls = [
        "https://data.sandbox.alpaca.markets",
        "https://data.alpaca.markets",
        "https://paper-api.alpaca.markets",
        "https://api.alpaca.markets"
    ]
    
    symbol = "AAPL"
    
    for base_url in base_urls:
        print(f"\n🌐 Testing base URL: {base_url}")
        print("=" * 60)
        
        # Test stock quote endpoint
        print(f"1. Testing stock quotes...")
        stock_url = f"{base_url}/v2/stocks/{symbol}/quotes/latest"
        try:
            response = requests.get(stock_url, headers=headers, timeout=10)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Stock quote success: {data}")
            else:
                print(f"   ❌ Error: {response.text[:200]}")
        except Exception as exc:
            print(f"   ❌ Exception: {exc}")
        
        # Test options snapshots endpoint
        print(f"2. Testing options snapshots...")
        options_url = f"{base_url}/v1beta1/options/snapshots"
        params = {"symbols": symbol, "feed": "sip"}
        try:
            response = requests.get(options_url, headers=headers, params=params, timeout=10)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Options snapshots success: {len(data.get('snapshots', {}))} contracts")
                # Show first few contract symbols
                if 'snapshots' in data:
                    contracts = list(data['snapshots'].keys())[:3]
                    print(f"   Sample contracts: {contracts}")
            else:
                print(f"   ❌ Error: {response.text[:200]}")
        except Exception as exc:
            print(f"   ❌ Exception: {exc}")
        
        # Test options quotes endpoint
        print(f"3. Testing options quotes...")
        quotes_url = f"{base_url}/v1beta1/options/quotes/latest"
        params = {"symbols": "AAPL241220C00150000", "feed": "sip"}  # Sample option symbol
        try:
            response = requests.get(quotes_url, headers=headers, params=params, timeout=10)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Options quotes success: {data}")
            else:
                print(f"   ❌ Error: {response.text[:200]}")
        except Exception as exc:
            print(f"   ❌ Exception: {exc}")

if __name__ == "__main__":
    test_alpaca_endpoints()
