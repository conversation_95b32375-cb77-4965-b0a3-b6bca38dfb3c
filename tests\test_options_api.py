#!/usr/bin/env python3
"""Test script to check FMP options API endpoints."""

import requests
from config import get_api_key

def test_fmp_options_endpoints():
    """Test various FMP options endpoints to find the correct one."""
    api_key = get_api_key("FMP_API_KEY")
    symbol = "AAPL"
    
    # Test different possible endpoints
    endpoints_to_test = [
        f"https://financialmodelingprep.com/api/v3/options/{symbol}?apikey={api_key}",
        f"https://financialmodelingprep.com/api/v4/options/{symbol}?apikey={api_key}",
        f"https://financialmodelingprep.com/api/v3/options-chain/{symbol}?apikey={api_key}",
        f"https://financialmodelingprep.com/api/v4/options-chain/{symbol}?apikey={api_key}",
        f"https://financialmodelingprep.com/api/v3/option/{symbol}?apikey={api_key}",
        f"https://financialmodelingprep.com/api/v4/option/{symbol}?apikey={api_key}",
    ]
    
    print(f"Testing FMP options endpoints for {symbol}...")
    print("=" * 60)
    
    for i, url in enumerate(endpoints_to_test, 1):
        print(f"\n{i}. Testing: {url}")
        try:
            response = requests.get(url, timeout=10)
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Response Type: {type(data)}")
                print(f"   Response Length: {len(data) if isinstance(data, (list, dict)) else 'N/A'}")
                
                if isinstance(data, list) and len(data) > 0:
                    print(f"   Sample Data Keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'Not a dict'}")
                    print(f"   ✅ SUCCESS: Found options data!")
                    return url, data
                elif isinstance(data, dict) and data:
                    print(f"   Sample Data Keys: {list(data.keys())}")
                    if any(key in data for key in ['options', 'chain', 'calls', 'puts']):
                        print(f"   ✅ SUCCESS: Found options data!")
                        return url, data
                else:
                    print(f"   ❌ Empty response")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Error Text: {response.text[:200]}")
                    
        except Exception as exc:
            print(f"   ❌ Exception: {exc}")
    
    print(f"\n❌ No working options endpoints found for FMP")
    return None, None

def test_alternative_options_sources():
    """Test alternative options data sources."""
    print(f"\n" + "=" * 60)
    print("Testing alternative options data sources...")
    print("=" * 60)
    
    # Test Yahoo Finance (free)
    symbol = "AAPL"
    yahoo_url = f"https://query1.finance.yahoo.com/v7/finance/options/{symbol}"
    
    print(f"\n1. Testing Yahoo Finance: {yahoo_url}")
    try:
        response = requests.get(yahoo_url, timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response Type: {type(data)}")
            
            if 'optionChain' in data:
                options_data = data['optionChain']['result'][0]
                print(f"   ✅ SUCCESS: Yahoo Finance has options data!")
                print(f"   Expiration Dates: {len(options_data.get('expirationDates', []))}")
                print(f"   Strikes: {len(options_data.get('strikes', []))}")
                
                if 'options' in options_data and options_data['options']:
                    calls = options_data['options'][0].get('calls', [])
                    puts = options_data['options'][0].get('puts', [])
                    print(f"   Calls: {len(calls)}, Puts: {len(puts)}")
                    
                    if calls:
                        sample_call = calls[0]
                        print(f"   Sample Call Keys: {list(sample_call.keys())}")
                
                return yahoo_url, data
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            
    except Exception as exc:
        print(f"   ❌ Exception: {exc}")
    
    return None, None

if __name__ == "__main__":
    # Test FMP first
    fmp_url, fmp_data = test_fmp_options_endpoints()
    
    # Test alternatives
    alt_url, alt_data = test_alternative_options_sources()
    
    print(f"\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    if fmp_url:
        print(f"✅ FMP Options API Working: {fmp_url}")
    else:
        print(f"❌ FMP Options API: Not available or not working")
    
    if alt_url:
        print(f"✅ Alternative Options API Working: Yahoo Finance")
        print(f"   Recommendation: Switch to Yahoo Finance for options data")
    else:
        print(f"❌ No working options APIs found")
