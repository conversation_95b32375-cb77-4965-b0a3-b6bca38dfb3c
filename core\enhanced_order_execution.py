#!/usr/bin/env python3
"""Enhanced Order Execution Engine

Advanced order execution with:
- Bracket orders (entry + stop + target)
- Limit order fallback logic
- Retry mechanisms with exponential backoff
- Order status tracking
- Safety system integration
- Advanced error handling
"""
import time
import json
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from enum import Enum
import threading

try:
    import alpaca_trade_api as tradeapi
    ALPACA_AVAILABLE = True
except ImportError:
    ALPACA_AVAILABLE = False


class OrderType(Enum):
    """Order types."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    BRACKET = "bracket"


class OrderStatus(Enum):
    """Order status."""
    PENDING = "pending"
    SUBMITTED = "submitted"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class EnhancedOrderExecutor:
    """Advanced order execution engine with safety integration."""
    
    def __init__(self, config_path: str = "data/execution_config.json"):
        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        
        # Order tracking
        self.active_orders = {}
        self.order_history = []
        
        # Alpaca client
        self.alpaca_client = None
        
        # Retry configuration
        self.max_retries = 3
        self.retry_delay = 1.0  # seconds
        self.backoff_multiplier = 2.0
        
        # Load configuration
        self.load_config()
        self.init_alpaca_client()
        
        # Start order monitoring
        self.monitoring_thread = None
        self.is_monitoring = False
        self.start_monitoring()
    
    def load_config(self):
        """Load execution configuration."""
        default_config = {
            "alpaca": {
                "api_key": "",
                "secret_key": "",
                "base_url": "https://paper-api.alpaca.markets",  # Paper trading by default
                "data_url": "https://data.alpaca.markets"
            },
            "execution": {
                "default_order_type": "limit",
                "limit_offset_pct": 0.1,  # 0.1% above/below market for limit orders
                "market_fallback_enabled": True,
                "market_fallback_delay": 30,  # seconds to wait before market fallback
                "max_slippage_pct": 0.5,  # Max acceptable slippage
                "order_timeout": 300,  # Order timeout in seconds
                "retry_enabled": True,
                "max_retries": 3
            },
            "bracket_orders": {
                "enabled": True,
                "default_stop_pct": 2.0,
                "default_target_pct": 6.0,
                "trailing_stop_enabled": True,
                "trailing_stop_pct": 1.0
            }
        }
        
        try:
            from pathlib import Path
            if Path(self.config_path).exists():
                with open(self.config_path, 'r') as f:
                    loaded_config = json.load(f)
                    self.config = {**default_config, **loaded_config}
            else:
                self.config = default_config
                self.save_config()
        except Exception as e:
            self.logger.error(f"Error loading execution config: {e}")
            self.config = default_config
    
    def save_config(self):
        """Save execution configuration."""
        try:
            from pathlib import Path
            Path(self.config_path).parent.mkdir(exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving execution config: {e}")
    
    def init_alpaca_client(self):
        """Initialize Alpaca trading client."""
        if not ALPACA_AVAILABLE:
            self.logger.warning("Alpaca API not available - install alpaca-trade-api")
            return
        
        try:
            alpaca_config = self.config["alpaca"]
            if alpaca_config["api_key"] and alpaca_config["secret_key"]:
                self.alpaca_client = tradeapi.REST(
                    alpaca_config["api_key"],
                    alpaca_config["secret_key"],
                    alpaca_config["base_url"],
                    api_version='v2'
                )
                
                # Test connection
                account = self.alpaca_client.get_account()
                self.logger.info(f"Alpaca connected - Account: {account.status}")
            else:
                self.logger.warning("Alpaca credentials not configured")
        except Exception as e:
            self.logger.error(f"Error initializing Alpaca client: {e}")
            self.alpaca_client = None
    
    def execute_bracket_order(self, symbol: str, quantity: int, side: str, 
                            entry_price: Optional[float] = None,
                            stop_loss_pct: Optional[float] = None,
                            take_profit_pct: Optional[float] = None) -> Dict:
        """Execute bracket order with entry, stop loss, and take profit."""
        
        # Validate with safety system
        from safety_system import get_safety_system
        safety = get_safety_system()
        
        # Get current price if not provided
        if entry_price is None:
            entry_price = self._get_current_price(symbol)
            if entry_price is None:
                return {"success": False, "error": "Could not get current price"}
        
        # Validate trade with safety system
        trade_request = {
            "symbol": symbol,
            "quantity": quantity,
            "price": entry_price,
            "action": "open"
        }
        
        validation = safety.validate_trade(trade_request)
        if not validation["approved"]:
            return {
                "success": False,
                "error": "Trade rejected by safety system",
                "details": validation
            }
        
        # Adjust quantity if safety system recommends
        if validation["adjusted_size"]:
            quantity = validation["adjusted_size"]
        
        # Calculate stop loss and take profit prices
        bracket_config = self.config["bracket_orders"]
        stop_pct = stop_loss_pct or bracket_config["default_stop_pct"]
        target_pct = take_profit_pct or bracket_config["default_target_pct"]
        
        if side.lower() == "buy":
            stop_price = entry_price * (1 - stop_pct / 100)
            target_price = entry_price * (1 + target_pct / 100)
        else:  # sell/short
            stop_price = entry_price * (1 + stop_pct / 100)
            target_price = entry_price * (1 - target_pct / 100)
        
        # Execute bracket order
        try:
            if self.alpaca_client:
                result = self._execute_alpaca_bracket_order(
                    symbol, quantity, side, entry_price, stop_price, target_price
                )
            else:
                result = self._execute_paper_bracket_order(
                    symbol, quantity, side, entry_price, stop_price, target_price
                )
            
            # Record with safety system if successful
            if result["success"]:
                trade_data = {
                    "trade_id": result.get("order_id", ""),
                    "symbol": symbol,
                    "quantity": quantity,
                    "price": entry_price,
                    "action": "open",
                    "pnl": 0.0  # Initial P&L is 0
                }
                safety.record_trade_execution(trade_data)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Bracket order execution error: {e}")
            return {"success": False, "error": str(e)}
    
    def execute_market_order(self, symbol: str, quantity: int, side: str) -> Dict:
        """Execute market order with retry logic."""
        return self._execute_order_with_retry(
            symbol, quantity, side, OrderType.MARKET
        )
    
    def execute_limit_order(self, symbol: str, quantity: int, side: str, 
                          limit_price: float, timeout: int = 300) -> Dict:
        """Execute limit order with market fallback."""
        # Try limit order first
        result = self._execute_order_with_retry(
            symbol, quantity, side, OrderType.LIMIT, limit_price
        )
        
        if not result["success"] and self.config["execution"]["market_fallback_enabled"]:
            self.logger.info(f"Limit order failed, falling back to market order for {symbol}")
            time.sleep(self.config["execution"]["market_fallback_delay"])
            return self.execute_market_order(symbol, quantity, side)
        
        return result
    
    def cancel_order(self, order_id: str) -> Dict:
        """Cancel an active order."""
        try:
            if self.alpaca_client:
                self.alpaca_client.cancel_order(order_id)
                result = {"success": True, "message": f"Order {order_id} cancelled"}
            else:
                # Paper trading cancellation
                if order_id in self.active_orders:
                    self.active_orders[order_id]["status"] = OrderStatus.CANCELLED
                result = {"success": True, "message": f"Paper order {order_id} cancelled"}
            
            self.logger.info(f"Order cancelled: {order_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error cancelling order {order_id}: {e}")
            return {"success": False, "error": str(e)}
    
    def get_order_status(self, order_id: str) -> Dict:
        """Get current status of an order."""
        try:
            if self.alpaca_client:
                order = self.alpaca_client.get_order(order_id)
                return {
                    "order_id": order_id,
                    "status": order.status,
                    "filled_qty": int(order.filled_qty or 0),
                    "remaining_qty": int(order.qty) - int(order.filled_qty or 0),
                    "avg_fill_price": float(order.filled_avg_price or 0)
                }
            else:
                # Paper trading status
                if order_id in self.active_orders:
                    order = self.active_orders[order_id]
                    return {
                        "order_id": order_id,
                        "status": order["status"].value,
                        "filled_qty": order.get("filled_qty", 0),
                        "remaining_qty": order.get("remaining_qty", 0),
                        "avg_fill_price": order.get("avg_fill_price", 0)
                    }
                else:
                    return {"error": "Order not found"}
                    
        except Exception as e:
            self.logger.error(f"Error getting order status {order_id}: {e}")
            return {"error": str(e)}
    
    def start_monitoring(self):
        """Start order monitoring thread."""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(target=self._monitor_orders, daemon=True)
            self.monitoring_thread.start()
    
    def stop_monitoring(self):
        """Stop order monitoring."""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
    
    def _execute_order_with_retry(self, symbol: str, quantity: int, side: str, 
                                order_type: OrderType, limit_price: Optional[float] = None) -> Dict:
        """Execute order with retry logic."""
        last_error = None
        
        for attempt in range(self.max_retries + 1):
            try:
                if self.alpaca_client:
                    result = self._execute_alpaca_order(symbol, quantity, side, order_type, limit_price)
                else:
                    result = self._execute_paper_order(symbol, quantity, side, order_type, limit_price)
                
                if result["success"]:
                    return result
                else:
                    last_error = result.get("error", "Unknown error")
                    
            except Exception as e:
                last_error = str(e)
                self.logger.warning(f"Order execution attempt {attempt + 1} failed: {e}")
            
            if attempt < self.max_retries:
                delay = self.retry_delay * (self.backoff_multiplier ** attempt)
                time.sleep(delay)
        
        return {"success": False, "error": f"All retry attempts failed. Last error: {last_error}"}
    
    def _execute_alpaca_bracket_order(self, symbol: str, quantity: int, side: str,
                                    entry_price: float, stop_price: float, target_price: float) -> Dict:
        """Execute bracket order via Alpaca."""
        try:
            # Create bracket order
            order = self.alpaca_client.submit_order(
                symbol=symbol,
                qty=quantity,
                side=side,
                type='limit',
                time_in_force='day',
                limit_price=entry_price,
                order_class='bracket',
                stop_loss={'stop_price': stop_price},
                take_profit={'limit_price': target_price}
            )
            
            return {
                "success": True,
                "order_id": order.id,
                "symbol": symbol,
                "quantity": quantity,
                "entry_price": entry_price,
                "stop_price": stop_price,
                "target_price": target_price,
                "message": f"Bracket order submitted for {symbol}"
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _execute_paper_bracket_order(self, symbol: str, quantity: int, side: str,
                                   entry_price: float, stop_price: float, target_price: float) -> Dict:
        """Execute paper bracket order."""
        order_id = f"paper_{symbol}_{int(time.time())}"
        
        # Create paper order record
        order_record = {
            "order_id": order_id,
            "symbol": symbol,
            "quantity": quantity,
            "side": side,
            "entry_price": entry_price,
            "stop_price": stop_price,
            "target_price": target_price,
            "status": OrderStatus.FILLED,  # Assume immediate fill for paper trading
            "timestamp": datetime.now().isoformat(),
            "filled_qty": quantity,
            "avg_fill_price": entry_price
        }
        
        self.active_orders[order_id] = order_record
        
        return {
            "success": True,
            "order_id": order_id,
            "symbol": symbol,
            "quantity": quantity,
            "entry_price": entry_price,
            "stop_price": stop_price,
            "target_price": target_price,
            "message": f"Paper bracket order executed for {symbol}"
        }
    
    def _execute_alpaca_order(self, symbol: str, quantity: int, side: str,
                            order_type: OrderType, limit_price: Optional[float] = None) -> Dict:
        """Execute single order via Alpaca."""
        try:
            order_params = {
                "symbol": symbol,
                "qty": quantity,
                "side": side,
                "type": order_type.value,
                "time_in_force": "day"
            }
            
            if order_type == OrderType.LIMIT and limit_price:
                order_params["limit_price"] = limit_price
            
            order = self.alpaca_client.submit_order(**order_params)
            
            return {
                "success": True,
                "order_id": order.id,
                "message": f"{order_type.value.title()} order submitted for {symbol}"
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _execute_paper_order(self, symbol: str, quantity: int, side: str,
                           order_type: OrderType, limit_price: Optional[float] = None) -> Dict:
        """Execute paper order."""
        order_id = f"paper_{symbol}_{int(time.time())}"
        
        # Get current price for paper execution
        current_price = self._get_current_price(symbol)
        if current_price is None:
            return {"success": False, "error": "Could not get current price"}
        
        # Simulate order execution
        fill_price = limit_price if order_type == OrderType.LIMIT else current_price
        
        order_record = {
            "order_id": order_id,
            "symbol": symbol,
            "quantity": quantity,
            "side": side,
            "type": order_type.value,
            "status": OrderStatus.FILLED,
            "timestamp": datetime.now().isoformat(),
            "filled_qty": quantity,
            "avg_fill_price": fill_price
        }
        
        self.active_orders[order_id] = order_record
        
        return {
            "success": True,
            "order_id": order_id,
            "message": f"Paper {order_type.value} order executed for {symbol}"
        }
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for symbol."""
        try:
            if self.alpaca_client:
                # Get latest trade
                trades = self.alpaca_client.get_latest_trade(symbol)
                return float(trades.price)
            else:
                # Mock price for paper trading
                import random
                return random.uniform(100, 300)
                
        except Exception as e:
            self.logger.error(f"Error getting price for {symbol}: {e}")
            return None
    
    def _monitor_orders(self):
        """Monitor active orders for status changes."""
        while self.is_monitoring:
            try:
                # Check order statuses
                for order_id in list(self.active_orders.keys()):
                    status = self.get_order_status(order_id)
                    if "error" not in status:
                        # Update order record
                        if order_id in self.active_orders:
                            self.active_orders[order_id].update(status)
                
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Order monitoring error: {e}")
                time.sleep(10)


# Global executor instance
_order_executor = None

def get_order_executor() -> EnhancedOrderExecutor:
    """Get the global order executor instance."""
    global _order_executor
    if _order_executor is None:
        _order_executor = EnhancedOrderExecutor()
    return _order_executor


if __name__ == "__main__":
    # Test the enhanced order executor
    executor = EnhancedOrderExecutor()
    
    print("📋 Testing Enhanced Order Execution")
    print("=" * 45)
    
    # Test bracket order
    result = executor.execute_bracket_order("AAPL", 10, "buy")
    print(f"✅ Bracket order: {'Success' if result['success'] else 'Failed'}")
    if result["success"]:
        print(f"   Order ID: {result['order_id']}")
        print(f"   Entry: ${result['entry_price']:.2f}")
        print(f"   Stop: ${result['stop_price']:.2f}")
        print(f"   Target: ${result['target_price']:.2f}")
    
    time.sleep(2)
    executor.stop_monitoring()
    print("📋 Order execution test complete!")
