#!/usr/bin/env python3
"""Test Script for TTM Trading System

Quick test to verify all components are working.
"""
import sys
import os

# Add all necessary directories to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'core'))
sys.path.insert(0, os.path.join(current_dir, 'gui'))
sys.path.insert(0, os.path.join(current_dir, 'scanners'))
sys.path.insert(0, os.path.join(current_dir, 'trading'))
sys.path.insert(0, os.path.join(current_dir, 'utils'))

def test_imports():
    """Test that all critical modules can be imported."""
    print("🧪 Testing TTM Trading System Components")
    print("=" * 50)
    
    tests = [
        ("Core Config", "config", "get_api_key"),
        ("Logger", "logger_util", "info"),
        ("TTM Scanner", "proper_ttm_squeeze_scanner", "TTMSqueezeScanner"),
        ("Alpaca Trading", "alpaca_trading", "AlpacaTrader"),
        ("GUI Interface", "tkinter_trading_interface", "TradingInterface"),
        ("Chat Core", "chat_core", "chat_gpt"),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, module_name, component in tests:
        try:
            module = __import__(module_name)
            if hasattr(module, component):
                print(f"✅ {test_name}: OK")
                passed += 1
            else:
                print(f"❌ {test_name}: Missing {component}")
                failed += 1
        except ImportError as e:
            print(f"❌ {test_name}: Import failed - {e}")
            failed += 1
        except Exception as e:
            print(f"⚠️  {test_name}: Error - {e}")
            failed += 1
    
    print(f"\n📊 Test Results: {passed} passed, {failed} failed")
    return failed == 0

def test_api_keys():
    """Test that API keys are loaded."""
    print("\n🔑 Testing API Key Configuration")
    print("-" * 30)
    
    try:
        from config import get_api_key, public_keys_summary
        
        keys_summary = public_keys_summary()
        
        for key_name, masked_value in keys_summary.items():
            if masked_value and masked_value != "****":
                print(f"✅ {key_name}: {masked_value}")
            else:
                print(f"❌ {key_name}: Not configured")
        
        return True
        
    except Exception as e:
        print(f"❌ API key test failed: {e}")
        return False

def test_alpaca_connection():
    """Test Alpaca connection."""
    print("\n🔗 Testing Alpaca Connection")
    print("-" * 25)
    
    try:
        from alpaca_trading import AlpacaTrader
        
        trader = AlpacaTrader(paper_trading=True)
        account = trader.get_account_info()
        
        if account:
            print(f"✅ Connected to Alpaca")
            print(f"   Account: {account.get('account_number', 'N/A')}")
            print(f"   Equity: ${float(account.get('equity', 0)):,.2f}")
            print(f"   Buying Power: ${float(account.get('buying_power', 0)):,.2f}")
            return True
        else:
            print("❌ Failed to get account info")
            return False
            
    except Exception as e:
        print(f"❌ Alpaca connection failed: {e}")
        return False

def test_ttm_scanner():
    """Test TTM scanner."""
    print("\n📊 Testing TTM Scanner")
    print("-" * 20)
    
    try:
        from proper_ttm_squeeze_scanner import TTMSqueezeScanner
        
        scanner = TTMSqueezeScanner()
        print("✅ TTM Scanner initialized")
        
        # Test with a single symbol
        result = scanner.scan_symbol('AAPL', '15min')
        if result:
            print(f"✅ Scanner test successful: {result['symbol']} - Grade {result['grade']}")
        else:
            print("✅ Scanner working (no squeeze found for AAPL)")
        
        return True
        
    except Exception as e:
        print(f"❌ TTM Scanner test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 TTM Trading System - Component Test")
    print("=" * 60)
    
    all_passed = True
    
    # Test imports
    if not test_imports():
        all_passed = False
    
    # Test API keys
    if not test_api_keys():
        all_passed = False
    
    # Test Alpaca
    if not test_alpaca_connection():
        all_passed = False
    
    # Test TTM Scanner
    if not test_ttm_scanner():
        all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! System is ready to use.")
        print("\n🚀 To start the GUI:")
        print("   python main.py")
    else:
        print("❌ Some tests failed. Check the errors above.")
        print("\n💡 Common fixes:")
        print("   - Install dependencies: pip install -r config/requirements.txt")
        print("   - Check API keys in config/config.env")
        print("   - Verify internet connection")

if __name__ == "__main__":
    main()
