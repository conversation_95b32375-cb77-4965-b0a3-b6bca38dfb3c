#!/usr/bin/env python3
"""Test the beginner-friendly formatter."""

from beginner_formatter import format_beginner_options_response

def test_beginner_formatter():
    """Test the beginner formatter with sample data."""
    
    # Sample options analysis result
    test_result = {
        "symbol": "AAPL",
        "strategy": "Bull Call Spread",
        "max_profit": 250.00,
        "max_loss": 150.00,
        "probability_of_profit": 0.65,
        "net_premium": 150.00,
        "breakeven_points": [235.00]
    }
    
    print("🧪 Testing Beginner-Friendly Formatter")
    print("=" * 50)
    
    formatted_response = format_beginner_options_response(test_result)
    print(formatted_response)
    
    print("\n" + "=" * 50)
    print("✅ This is the beginner-friendly format we want!")
    print("📊 Clean tables, plain English, no jargon")

if __name__ == "__main__":
    test_beginner_formatter()
