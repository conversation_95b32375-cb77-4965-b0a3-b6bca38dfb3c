"""Proper TTM Squeeze Scanner - Matches Think or Swim Logic

This scanner implements the standard TTM Squeeze detection that matches
what Think or Swim and other platforms use:

1. Squeeze Condition: Bollinger Bands inside Keltner Channels
2. Squeeze Release: Bollinger Bands break outside Keltner Channels
3. Momentum: Using proper momentum oscillator
4. Histogram: Momentum histogram analysis

This should find the same squeezes that Think or Swim finds.
"""
from __future__ import annotations

import asyncio
import aiohttp
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import requests

try:
    from config import get_api_key
except ImportError:
    def get_api_key(key_name):
        import os
        # Try to load from config.env file
        config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'config.env')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#'):
                        if '=' in line:
                            env_key, env_value = line.strip().split('=', 1)
                            if env_key == key_name:
                                return env_value
        return os.getenv(key_name)

try:
    from logger_util import info, warning
except ImportError:
    def info(msg): print(f"INFO: {msg}")
    def warning(msg): print(f"WARNING: {msg}")

# Get API key - use environment variable or hardcoded key
import os
FMP_API_KEY = os.getenv('FMP_API_KEY') or 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'
FMP_BASE_URL = 'https://financialmodelingprep.com/api/v3'

# Large cap stocks for scanning
LARGE_CAP_STOCKS = [
    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'BRK.B', 'UNH', 'JNJ',
    'V', 'WMT', 'XOM', 'LLY', 'JPM', 'PG', 'MA', 'HD', 'CVX', 'ABBV',
    'BAC', 'ORCL', 'KO', 'AVGO', 'PEP', 'COST', 'TMO', 'MRK', 'ACN', 'CSCO',
    'ABT', 'DHR', 'VZ', 'ADBE', 'CRM', 'NKE', 'TXN', 'LIN', 'WFC', 'NEE',
    'RTX', 'PM', 'UPS', 'QCOM', 'SPGI', 'HON', 'T', 'INTU', 'COP', 'IBM'
]

class ProperTTMSqueezeScanner:
    """Proper TTM Squeeze scanner that matches Think or Swim logic."""
    
    def __init__(self):
        self.api_key = FMP_API_KEY
        if not self.api_key:
            raise ValueError("FMP_API_KEY not found! Please set your API key in config/config.env")
    
    def get_historical_data(self, symbol: str, timeframe: str = '5min', days: int = 30) -> Optional[pd.DataFrame]:
        """Get historical data for a symbol."""
        try:
            # Map timeframes
            timeframe_map = {
                '1min': '1min',
                '5min': '5min', 
                '15min': '15min',
                '30min': '30min',
                '1hour': '1hour',
                '1day': 'daily-price-full'
            }
            
            if timeframe == '1day':
                url = f"{FMP_BASE_URL}/historical-price-full/{symbol}?apikey={self.api_key}"
            else:
                tf = timeframe_map.get(timeframe, '5min')
                url = f"{FMP_BASE_URL}/historical-chart/{tf}/{symbol}?apikey={self.api_key}"
            
            response = requests.get(url, timeout=10)
            data = response.json()
            
            if timeframe == '1day' and 'historical' in data:
                df = pd.DataFrame(data['historical'])
            elif isinstance(data, list):
                df = pd.DataFrame(data)
            else:
                return None
            
            if df.empty:
                return None
            
            # Ensure we have the required columns
            required_cols = ['date', 'open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_cols):
                return None
            
            # Convert to proper types
            df['date'] = pd.to_datetime(df['date'])
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Sort by date (oldest first)
            df = df.sort_values('date').reset_index(drop=True)
            
            # Take last N days of data
            if len(df) > days * 100:  # Rough estimate for intraday data
                df = df.tail(days * 100)
            
            return df
            
        except Exception as e:
            warning(f"Failed to get data for {symbol}: {e}")
            return None
    
    def calculate_bollinger_bands(self, df: pd.DataFrame, period: int = 20, std_dev: float = 2.0) -> pd.DataFrame:
        """Calculate Bollinger Bands."""
        df = df.copy()
        
        # Simple Moving Average
        df['bb_middle'] = df['close'].rolling(window=period).mean()
        
        # Standard deviation
        df['bb_std'] = df['close'].rolling(window=period).std()
        
        # Upper and lower bands
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * std_dev)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * std_dev)
        
        return df
    
    def calculate_keltner_channels(self, df: pd.DataFrame, period: int = 20, multiplier: float = 1.5) -> pd.DataFrame:
        """Calculate Keltner Channels."""
        df = df.copy()
        
        # Exponential Moving Average of close
        df['kc_middle'] = df['close'].ewm(span=period).mean()
        
        # True Range
        df['tr1'] = df['high'] - df['low']
        df['tr2'] = abs(df['high'] - df['close'].shift(1))
        df['tr3'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
        
        # Average True Range
        df['atr'] = df['true_range'].ewm(span=period).mean()
        
        # Keltner Channels
        df['kc_upper'] = df['kc_middle'] + (df['atr'] * multiplier)
        df['kc_lower'] = df['kc_middle'] - (df['atr'] * multiplier)
        
        return df
    
    def calculate_momentum(self, df: pd.DataFrame, period: int = 12) -> pd.DataFrame:
        """Calculate momentum oscillator (like Think or Swim)."""
        df = df.copy()
        
        # Linear regression of close prices
        def linear_regression_slope(series):
            if len(series) < 2:
                return 0
            x = np.arange(len(series))
            y = series.values
            if np.isnan(y).any():
                return 0
            slope = np.polyfit(x, y, 1)[0]
            return slope
        
        # Calculate momentum as linear regression slope
        df['momentum'] = df['close'].rolling(window=period).apply(linear_regression_slope, raw=False)
        
        # Normalize momentum
        df['momentum'] = df['momentum'] * 100
        
        return df
    
    def detect_ttm_squeeze(self, df: pd.DataFrame) -> Dict:
        """Detect TTM Squeeze conditions using STRICT 5 criteria."""
        if len(df) < 50:
            return {'meets_criteria': False, 'reason': 'Insufficient data'}

        # Calculate ALL indicators first
        df = self.calculate_bollinger_bands(df)
        df = self.calculate_keltner_channels(df)
        df = self.calculate_momentum(df)

        # Calculate 8EMA
        df['ema8'] = df['close'].ewm(span=8).mean()

        # Calculate ATR for ATR flip detection
        df['high_low'] = df['high'] - df['low']
        df['high_close'] = abs(df['high'] - df['close'].shift(1))
        df['low_close'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
        df['atr'] = df['true_range'].rolling(window=14).mean()

        # Get latest values
        latest = df.iloc[-1]

        # ENHANCED CRITERIA: CORE MOMENTUM + PROFITABILITY FILTERS
        core_criteria = {}
        profitability_filters = {}
        bonus_criteria = {}

        # PROFITABILITY FILTERS (Must pass these first!)

        # 1. PRICE RANGE FILTER (Reasonable range for trading)
        price_in_range = 15 <= latest['close'] <= 300  # More realistic range
        profitability_filters['price_in_range'] = price_in_range

        # 2. VOLUME FILTER (Reasonable volume requirements)
        if len(df) >= 20:
            avg_volume_20 = df['volume'].rolling(20).mean().iloc[-1]
            current_volume = latest['volume']
            volume_above_average = current_volume > (avg_volume_20 * 1.2)  # 120% of average (more realistic)

            # Check for volume trend (less strict)
            recent_volumes = df['volume'].iloc[-3:].values
            volume_trending = recent_volumes[-1] > recent_volumes[-3]  # Just needs to be higher than 3 periods ago
        else:
            volume_above_average = False
            volume_trending = False
        profitability_filters['volume_above_average'] = volume_above_average
        profitability_filters['volume_trending'] = volume_trending

        # 3. MOMENTUM STRENGTH FILTER (Reasonable momentum threshold)
        momentum_strength = abs(latest['momentum'])
        strong_momentum = momentum_strength > 0.5  # Lower threshold (more realistic)
        profitability_filters['strong_momentum'] = strong_momentum

        # 4. VOLATILITY FILTER (Reasonable movement for profit)
        if len(df) >= 14:
            price_range_14 = (df['high'].rolling(14).max() - df['low'].rolling(14).min()).iloc[-1]
            current_price = latest['close']
            volatility_sufficient = (price_range_14 / current_price) > 0.03  # 3% range minimum (more realistic)
        else:
            volatility_sufficient = False
        profitability_filters['volatility_sufficient'] = volatility_sufficient

        # CORE CRITERIA (Tightened for quality)

        # 1. 8EMA RISING (Reasonable trend requirement)
        if len(df) >= 8:
            ema8_values = df['ema8'].iloc[-4:].values  # Last 4 values (more realistic)
            ema8_rising = all(ema8_values[i] < ema8_values[i+1] for i in range(len(ema8_values)-1))
            # Price should be above EMA8 for bullish bias
            price_above_ema = latest['close'] > latest['ema8']
            ema8_rising = ema8_rising and price_above_ema
        else:
            ema8_rising = False
        core_criteria['ema8_rising'] = ema8_rising

        # 2. ATR FLIP (Reasonable volatility expansion)
        if len(df) >= 15:
            atr_values = df['atr'].iloc[-7:].values  # Last 7 ATR values
            # More realistic: ATR trending up
            atr_recent_avg = np.mean(atr_values[-3:])
            atr_previous_avg = np.mean(atr_values[-6:-3])
            atr_flip = atr_recent_avg > atr_previous_avg  # Just needs to be increasing
        else:
            atr_flip = False
        core_criteria['atr_flip'] = atr_flip

        # 3. HISTOGRAM PATTERN (Reasonable momentum building)
        if len(df) >= 8:
            momentum_values = df['momentum'].iloc[-4:].values  # Last 4 momentum values
            # More realistic: 2 rising bars OR positive momentum with trend
            two_rising = momentum_values[-1] > momentum_values[-2]
            positive_momentum = momentum_values[-1] > 0
            momentum_trend = momentum_values[-1] > momentum_values[-3]  # Higher than 3 periods ago
            histogram_pattern = (two_rising and positive_momentum) or (positive_momentum and momentum_trend)
        else:
            histogram_pattern = False
        core_criteria['histogram_pattern'] = histogram_pattern

        # 4. MOMENTUM RISING (Reasonable momentum trend)
        if len(df) >= 8:
            momentum_values = df['momentum'].iloc[-4:].values  # Last 4 momentum values
            # More realistic: trending up over 4 periods
            momentum_rising = momentum_values[-1] > momentum_values[-2] and momentum_values[-2] > momentum_values[-4]
        else:
            momentum_rising = False
        core_criteria['momentum_rising'] = momentum_rising

        # BONUS CRITERIA (Nice to have, not required!)

        # 5. BB INSIDE KC (Squeeze condition - BONUS ONLY)
        bb_inside_kc = (latest['bb_upper'] <= latest['kc_upper'] and
                       latest['bb_lower'] >= latest['kc_lower'])
        bonus_criteria['bb_inside_kc'] = bb_inside_kc

        # PROFITABILITY-FOCUSED SCORING SYSTEM
        core_count = sum(core_criteria.values())  # Out of 4
        profitability_count = sum(profitability_filters.values())  # Out of 4
        has_squeeze_bonus = bb_inside_kc

        # MUST PASS PROFITABILITY FILTERS FIRST! (More realistic)
        passes_profitability = profitability_count >= 2  # Need 2 out of 4 profitability filters (more realistic)

        # Quality levels based on CORE criteria + PROFITABILITY
        meets_all_core = core_count == 4 and passes_profitability  # All 4 core + profitable
        meets_most_core = core_count >= 3 and passes_profitability  # 3+ core + profitable
        meets_some_core = core_count >= 2 and passes_profitability  # 2+ core + profitable

        # Combine all criteria for compatibility
        all_criteria = {**core_criteria, **profitability_filters, **bonus_criteria}
        total_criteria_count = core_count + profitability_count + (1 if has_squeeze_bonus else 0)

        # Define quality levels for compatibility (MUCH STRICTER NOW)
        meets_all_criteria = meets_all_core and has_squeeze_bonus  # Perfect: 4 core + profitable + squeeze
        meets_most_criteria = meets_all_core or (meets_most_core and has_squeeze_bonus)  # Strong: 4 core + profitable OR 3+ core + profitable + squeeze
        meets_some_criteria = meets_some_core  # Decent: 2+ core + profitable (minimum viable trade)

        # Build detailed response with profitability data
        result = {
            'meets_criteria': meets_all_criteria,
            'meets_most_criteria': meets_most_criteria,
            'meets_some_criteria': meets_some_criteria,
            'core_count': core_count,
            'profitability_count': profitability_count,
            'passes_profitability': passes_profitability,
            'criteria_count': total_criteria_count,  # For backward compatibility
            'has_squeeze_bonus': has_squeeze_bonus,
            'bb_inside_kc': bb_inside_kc,
            'ema8_rising': core_criteria['ema8_rising'],
            'atr_flip': core_criteria['atr_flip'],
            'histogram_pattern': core_criteria['histogram_pattern'],
            'momentum_rising': core_criteria['momentum_rising'],
            'momentum_value': latest['momentum'],
            'ema8_value': latest['ema8'],
            'atr_value': latest['atr'],
            'bb_upper': latest['bb_upper'],
            'bb_lower': latest['bb_lower'],
            'kc_upper': latest['kc_upper'],
            'kc_lower': latest['kc_lower'],
            'close': latest['close'],
            'volume': latest['volume'],
            'core_criteria': core_criteria,
            'profitability_filters': profitability_filters,
            'bonus_criteria': bonus_criteria,
            'criteria_summary': all_criteria
        }

        # Add failure reason if criteria not met
        if not meets_some_criteria:
            failed_core = [name for name, passed in core_criteria.items() if not passed]
            failed_profit = [name for name, passed in profitability_filters.items() if not passed]

            if not passes_profitability:
                result['reason'] = f"Failed profitability filters: {', '.join(failed_profit)} (needs 2+ profitability filters)"
            else:
                result['reason'] = f"Failed core criteria: {', '.join(failed_core)} (needs 2+ core criteria)"

        return result
    
    def grade_opportunity(self, squeeze_data: Dict) -> str:
        """Grade based on PROFITABILITY + CORE MOMENTUM with squeeze as bonus."""
        core_count = squeeze_data['core_count']
        profitability_count = squeeze_data['profitability_count']
        passes_profitability = squeeze_data['passes_profitability']
        has_squeeze_bonus = squeeze_data['has_squeeze_bonus']
        momentum_strength = abs(squeeze_data['momentum_value'])

        # MUST PASS PROFITABILITY FIRST!
        if not passes_profitability:
            return 'F'  # Automatic fail if not profitable

        # Base grade on CORE CRITERIA + PROFITABILITY
        if core_count == 4 and profitability_count == 4:
            # Perfect setup: All core + all profitability
            if has_squeeze_bonus:
                base_grade = 'A+'  # Perfect: 4 core + 4 profit + squeeze
            else:
                base_grade = 'A+'  # Excellent: 4 core + 4 profit
        elif core_count == 4:
            # All core criteria but some profitability concerns
            if has_squeeze_bonus:
                base_grade = 'A'   # Strong: 4 core + squeeze + some profit
            else:
                base_grade = 'A'   # Good: 4 core + some profit
        elif core_count == 3 and profitability_count == 4:
            # Strong momentum with perfect profitability
            if has_squeeze_bonus:
                base_grade = 'A'   # Strong: 3 core + 4 profit + squeeze
            else:
                base_grade = 'B+'  # Good: 3 core + 4 profit
        elif core_count == 3:
            # 3 core criteria with basic profitability
            if has_squeeze_bonus:
                base_grade = 'B+'  # Decent: 3 core + squeeze + some profit
            else:
                base_grade = 'B'   # Fair: 3 core + some profit
        elif core_count == 2:
            # Minimum viable setup
            if has_squeeze_bonus:
                base_grade = 'B'   # Minimum: 2 core + squeeze + profit
            else:
                base_grade = 'C'   # Weak: 2 core + profit
        else:
            return 'F'  # Less than 2 core criteria = fail

        # Adjust grade based on momentum strength and profitability
        if momentum_strength > 5.0 and profitability_count == 4:
            # Exceptional momentum + perfect profitability - upgrade
            upgrades = {'A': 'A+', 'B+': 'A', 'B': 'B+', 'C': 'B'}
            return upgrades.get(base_grade, base_grade)
        elif momentum_strength > 3.0:
            # Strong momentum - small upgrade
            if base_grade in ['B+', 'B', 'C']:
                upgrades = {'B+': 'A', 'B': 'B+', 'C': 'B'}
                return upgrades.get(base_grade, base_grade)
        elif momentum_strength < 1.0:
            # Weak momentum - downgrade
            downgrades = {'A+': 'A', 'A': 'B+', 'B+': 'B', 'B': 'C'}
            return downgrades.get(base_grade, base_grade)

        return base_grade
    
    def scan_symbol(self, symbol: str, timeframe: str = '5min') -> Optional[Dict]:
        """Scan a single symbol for TTM squeeze using STRICT 5 criteria."""
        try:
            df = self.get_historical_data(symbol, timeframe)
            if df is None or len(df) < 50:
                return None

            squeeze_data = self.detect_ttm_squeeze(df)

            # Return ONLY if meets PROFITABILITY + CORE criteria (quality-focused approach)
            if squeeze_data['meets_some_criteria']:  # At least 2 core + 3 profitability
                grade = self.grade_opportunity(squeeze_data)

                # Calculate better risk/reward based on volatility
                atr_value = squeeze_data['atr_value']
                entry_price = squeeze_data['close']

                # Dynamic stop loss based on ATR (more realistic)
                stop_loss = entry_price - (atr_value * 1.5)  # 1.5x ATR stop

                # Dynamic target based on momentum strength
                momentum_strength = abs(squeeze_data['momentum_value'])
                if momentum_strength > 3.0:
                    target_multiplier = 3.0  # Strong momentum = bigger target
                elif momentum_strength > 2.0:
                    target_multiplier = 2.5
                else:
                    target_multiplier = 2.0

                target_price = entry_price + (atr_value * target_multiplier)
                risk_reward = (target_price - entry_price) / (entry_price - stop_loss)

                return {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'grade': grade,
                    'core_count': squeeze_data['core_count'],
                    'profitability_count': squeeze_data['profitability_count'],
                    'passes_profitability': squeeze_data['passes_profitability'],
                    'criteria_count': squeeze_data['criteria_count'],  # Total including all
                    'has_squeeze_bonus': squeeze_data['has_squeeze_bonus'],
                    'meets_all_criteria': squeeze_data['meets_criteria'],
                    'meets_most_criteria': squeeze_data['meets_most_criteria'],
                    'bb_inside_kc': squeeze_data['bb_inside_kc'],
                    'ema8_rising': squeeze_data['ema8_rising'],
                    'atr_flip': squeeze_data['atr_flip'],
                    'histogram_pattern': squeeze_data['histogram_pattern'],
                    'momentum_rising': squeeze_data['momentum_rising'],
                    'momentum_value': round(squeeze_data['momentum_value'], 2),
                    'ema8_value': round(squeeze_data['ema8_value'], 2),
                    'atr_value': round(squeeze_data['atr_value'], 2),
                    'price': round(squeeze_data['close'], 2),
                    'volume': squeeze_data['volume'],
                    'confidence': self.calculate_confidence(squeeze_data),
                    'entry_price': round(entry_price, 2),
                    'stop_loss': round(stop_loss, 2),
                    'target_price': round(target_price, 2),
                    'risk_reward': round(risk_reward, 1),
                    'core_criteria': squeeze_data['core_criteria'],
                    'profitability_filters': squeeze_data['profitability_filters'],
                    'bonus_criteria': squeeze_data['bonus_criteria'],
                    'criteria_details': squeeze_data['criteria_summary']
                }
            else:
                # Log why it failed for debugging
                if not squeeze_data['passes_profitability']:
                    info(f"❌ {symbol} failed profitability: {squeeze_data.get('reason', 'Unknown')}")
                elif squeeze_data['core_count'] <= 1:
                    info(f"❌ {symbol} failed core criteria: {squeeze_data.get('reason', 'Unknown')}")
                return None

        except Exception as e:
            warning(f"Error scanning {symbol}: {e}")
            return None
    
    def calculate_confidence(self, squeeze_data: Dict) -> float:
        """Calculate confidence based on PROFITABILITY + CORE CRITERIA."""
        core_count = squeeze_data['core_count']
        profitability_count = squeeze_data['profitability_count']
        passes_profitability = squeeze_data['passes_profitability']
        has_squeeze_bonus = squeeze_data['has_squeeze_bonus']
        momentum_strength = abs(squeeze_data['momentum_value'])

        # MUST PASS PROFITABILITY FIRST!
        if not passes_profitability:
            return 0.1  # Very low confidence if not profitable

        # Base confidence on CORE CRITERIA + PROFITABILITY
        if core_count == 4 and profitability_count == 4:
            confidence = 0.95  # Exceptional confidence - perfect setup
        elif core_count == 4:
            confidence = 0.85  # Very high confidence - all core + some profit
        elif core_count == 3 and profitability_count == 4:
            confidence = 0.8   # High confidence - most core + perfect profit
        elif core_count == 3:
            confidence = 0.7   # Good confidence - most core + some profit
        elif core_count == 2 and profitability_count == 4:
            confidence = 0.65  # Medium confidence - some core + perfect profit
        elif core_count == 2:
            confidence = 0.55  # Fair confidence - minimum viable
        else:
            confidence = 0.3   # Low confidence - below minimum

        # Profitability bonus (critical for real trading)
        profitability_bonus = profitability_count * 0.05  # 5% per profitability filter
        confidence += profitability_bonus

        # Squeeze bonus adds confidence (but not required)
        if has_squeeze_bonus:
            confidence += 0.05  # 5% bonus for squeeze (reduced importance)

        # Adjust based on momentum strength (critical for profits)
        if momentum_strength > 5.0:
            confidence += 0.2   # Exceptional momentum
        elif momentum_strength > 3.0:
            confidence += 0.15  # Very strong momentum
        elif momentum_strength > 2.0:
            confidence += 0.1   # Strong momentum
        elif momentum_strength > 1.0:
            confidence += 0.05  # Good momentum
        elif momentum_strength < 1.0:
            confidence -= 0.15  # Weak momentum (major penalty)

        return min(max(confidence, 0.1), 1.0)  # Keep between 0.1 and 1.0
    
    def scan_all_symbols(self, timeframes: List[str] = ['5min', '15min', '1hour']) -> List[Dict]:
        """Scan all symbols across multiple timeframes."""
        info(f"🔍 Scanning {len(LARGE_CAP_STOCKS)} symbols across {len(timeframes)} timeframes")
        
        opportunities = []
        
        for symbol in LARGE_CAP_STOCKS:
            for timeframe in timeframes:
                opportunity = self.scan_symbol(symbol, timeframe)
                if opportunity:
                    opportunities.append(opportunity)
        
        # Sort by grade and confidence
        grade_order = {'A+': 0, 'A': 1, 'B': 2, 'C': 3, 'D': 4, 'F': 5}
        opportunities.sort(key=lambda x: (grade_order.get(x['grade'], 5), -x['confidence']))
        
        return opportunities


def run_proper_ttm_scan() -> str:
    """Run the STRICT TTM squeeze scan with 5 criteria."""
    try:
        scanner = ProperTTMSqueezeScanner()
        opportunities = scanner.scan_all_symbols()

        if opportunities:
            result = "💎 PROFITABLE TTM OPPORTUNITIES (QUALITY FOCUSED):\n\n"
            result += "🔥 CORE CRITERIA: 8EMA Rising | ATR Flip | Histogram Pattern | Momentum Rising\n"
            result += "💰 PROFITABILITY FILTERS: Price Range | Volume | Momentum Strength | Volatility\n"
            result += "🎁 BONUS: BB inside KC (Squeeze) - Nice to have, not required!\n\n"

            # Group by grade (quality-focused)
            a_plus_setups = [opp for opp in opportunities if opp['grade'] == 'A+']
            a_setups = [opp for opp in opportunities if opp['grade'] in ['A', 'B+']]
            b_setups = [opp for opp in opportunities if opp['grade'] in ['B', 'C']]

            if a_plus_setups:
                result += "🏆 PREMIUM SETUPS (Grade A+):\n"
                for opp in a_plus_setups:
                    squeeze_bonus = "🎁 +Squeeze" if opp['has_squeeze_bonus'] else ""
                    profit_score = f"💰 {opp['profitability_count']}/4"
                    result += f"🔥 {opp['symbol']} ({opp['timeframe']}) - Grade {opp['grade']} ({opp['confidence']*100:.0f}%) {squeeze_bonus} {profit_score}\n"
                    result += f"   💰 Price: ${opp['price']:.2f} | Volume: {opp['volume']:,} | Momentum: {opp['momentum_value']:.2f}\n"
                    result += f"   🎯 Entry: ${opp['entry_price']:.2f} | Stop: ${opp['stop_loss']:.2f} | Target: ${opp['target_price']:.2f} | R/R: {opp['risk_reward']:.1f}:1\n\n"

            if a_setups:
                result += "⭐ QUALITY SETUPS (Grade A/B+):\n"
                for opp in a_setups[:10]:  # Limit to top 10
                    squeeze_bonus = "🎁 +Squeeze" if opp['has_squeeze_bonus'] else ""
                    profit_score = f"💰 {opp['profitability_count']}/4"
                    result += f"📈 {opp['symbol']} ({opp['timeframe']}) - Grade {opp['grade']} ({opp['confidence']*100:.0f}%) {squeeze_bonus} {profit_score}\n"
                    result += f"   💰 Price: ${opp['price']:.2f} | Momentum: {opp['momentum_value']:.2f} | R/R: {opp['risk_reward']:.1f}:1\n"

                    # Show what's missing for transparency
                    missing_core = [name for name, passed in opp['core_criteria'].items() if not passed]
                    missing_profit = [name for name, passed in opp['profitability_filters'].items() if not passed]
                    if missing_core:
                        result += f"   ⚠️ Missing Core: {', '.join(missing_core)}\n"
                    if missing_profit:
                        result += f"   ⚠️ Missing Profit: {', '.join(missing_profit)}\n"
                    result += "\n"

            if b_setups:
                result += "📊 MARGINAL SETUPS (Grade B/C) - Use Caution:\n"
                for opp in b_setups[:5]:  # Limit to top 5
                    squeeze_bonus = "🎁 +Squeeze" if opp['has_squeeze_bonus'] else ""
                    profit_score = f"💰 {opp['profitability_count']}/4"
                    result += f"⚠️ {opp['symbol']} ({opp['timeframe']}) - Grade {opp['grade']} ({opp['confidence']*100:.0f}%) {squeeze_bonus} {profit_score}\n"
                    result += f"   💰 Price: ${opp['price']:.2f} | R/R: {opp['risk_reward']:.1f}:1\n\n"

            result += f"📊 QUALITY SUMMARY: {len(opportunities)} profitable opportunities found\n"
            result += f"   🏆 {len(a_plus_setups)} premium (A+) | ⭐ {len(a_setups)} quality (A/B+) | 📊 {len(b_setups)} marginal (B/C)\n"
            result += f"   🎁 {len([o for o in opportunities if o['has_squeeze_bonus']])} with squeeze bonus\n"
            result += f"   💰 Avg Profitability Score: {sum(o['profitability_count'] for o in opportunities) / len(opportunities):.1f}/4\n"
            return result
        else:
            return """📊 No PROFITABLE opportunities found meeting quality criteria.

💰 PROFITABILITY FILTERS (Need 2+ to qualify):
1. 💰 Price Range ($15-$300) - Reasonable trading range
2. 📊 Volume Above Average (120%+ of 20-day avg)
3. 🚀 Strong Momentum (>0.5 threshold)
4. 📈 Sufficient Volatility (3%+ range for profit potential)

🔥 CORE MOMENTUM CRITERIA (Need 2+ to qualify):
1. ✅ 8EMA Rising (4 consecutive periods, price above EMA)
2. ✅ ATR Flip (Recent ATR > Previous ATR average)
3. ✅ Histogram Pattern (2+ rising bars OR positive momentum trend)
4. ✅ Momentum Rising (Trending up over 4 periods)

🎁 BONUS CRITERIA (Nice to have):
5. 🎁 BB inside KC (Squeeze condition)

💡 QUALITY OVER QUANTITY - Only setups with real profit potential pass!
Current market may be choppy or lacking strong momentum trends."""

    except Exception as e:
        return f"❌ Error running STRICT TTM scan: {str(e)}\n\n💡 Make sure FMP_API_KEY is set in config/config.env"


if __name__ == "__main__":
    print("🚀 Testing Proper TTM Squeeze Scanner")
    print("=" * 50)
    result = run_proper_ttm_scan()
    print(result)
