#!/usr/bin/env python3
"""Test Alpaca options API directly to see what data we get."""

import requests
import json

def test_alpaca_options():
    """Test Alpaca options API with the exact endpoint and credentials provided."""
    
    url = "https://data.sandbox.alpaca.markets/v1beta1/options/bars?limit=1000&sort=asc"
    
    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": "PK43FUDB28UZYZ87BT2V",
        "APCA-API-SECRET-KEY": "ICvalUW5EpIlQnInIidzIoDXrqQMdJ2CZJCD7RKg"
    }
    
    print("🔍 Testing Alpaca Options API...")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        
        print(f"\n📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"\n✅ SUCCESS! Got JSON response")
                print(f"📊 Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                
                if isinstance(data, dict) and "bars" in data:
                    bars = data["bars"]
                    print(f"📊 Number of option symbols: {len(bars)}")
                    
                    # Show first few option symbols
                    symbols = list(bars.keys())[:5]
                    print(f"📊 Sample symbols: {symbols}")
                    
                    # Show data for first symbol
                    if symbols:
                        first_symbol = symbols[0]
                        first_data = bars[first_symbol]
                        print(f"\n📊 Data for {first_symbol}:")
                        print(f"   Number of bars: {len(first_data)}")
                        if first_data:
                            print(f"   Latest bar: {first_data[-1]}")
                
                # Print full response (truncated)
                response_text = json.dumps(data, indent=2)
                if len(response_text) > 2000:
                    print(f"\n📊 Response (first 2000 chars):\n{response_text[:2000]}...")
                else:
                    print(f"\n📊 Full Response:\n{response_text}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response.text[:500]}")
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Error response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_with_symbol_filter():
    """Test with specific symbol filter to get AAPL options."""
    
    url = "https://data.sandbox.alpaca.markets/v1beta1/options/bars"
    
    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": "PK43FUDB28UZYZ87BT2V",
        "APCA-API-SECRET-KEY": "ICvalUW5EpIlQnInIidzIoDXrqQMdJ2CZJCD7RKg"
    }
    
    # Try with AAPL symbol filter
    params = {
        "symbols": "AAPL*",  # Wildcard for all AAPL options
        "limit": 100,
        "sort": "asc",
        "timeframe": "1Day"
    }
    
    print("\n🔍 Testing with AAPL symbol filter...")
    print(f"URL: {url}")
    print(f"Params: {params}")
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=15)
        
        print(f"\n📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS! Got JSON response")
            
            if isinstance(data, dict) and "bars" in data:
                bars = data["bars"]
                print(f"📊 Number of AAPL option symbols: {len(bars)}")
                
                # Show first few option symbols
                symbols = list(bars.keys())[:10]
                print(f"📊 Sample AAPL symbols: {symbols}")
                
                # Parse one symbol to test our parser
                if symbols:
                    test_symbol = symbols[0]
                    print(f"\n🧪 Testing symbol parser on: {test_symbol}")
                    
                    # Simple parsing test
                    import re
                    match = re.search(r'(\d{6}[CP])', test_symbol)
                    if match:
                        date_cp_start = match.start()
                        underlying = test_symbol[:date_cp_start]
                        date_cp = test_symbol[date_cp_start:date_cp_start+7]
                        strike_part = test_symbol[date_cp_start+7:date_cp_start+15]
                        
                        print(f"   Underlying: {underlying}")
                        print(f"   Date+Type: {date_cp}")
                        print(f"   Strike: {strike_part} -> ${float(strike_part)/1000:.2f}")
            else:
                print(f"📊 Response structure: {data}")
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Error response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_alpaca_options()
    test_with_symbol_filter()
