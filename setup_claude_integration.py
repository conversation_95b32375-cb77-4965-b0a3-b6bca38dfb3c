#!/usr/bin/env python3
"""
Claude Desktop Integration Setup for TotalRecall

This script sets up <PERSON> to work with your alpaca-mcp-server,
giving you natural language trading capabilities alongside your existing TotalRecall system.
"""

import os
import json
import shutil
from pathlib import Path

def setup_claude_integration():
    """Setup Claude Desktop integration."""
    print("🤖 CLAUDE DESKTOP INTEGRATION SETUP")
    print("=" * 50)
    
    # Step 1: Check for alpaca-mcp-server
    print("\n📍 Step 1: Locating alpaca-mcp-server...")
    mcp_server_path = locate_mcp_server()
    
    if not mcp_server_path:
        print("❌ alpaca-mcp-server not found!")
        print("Please run setup_enhanced_mcp_integration.py first")
        return False
    
    print(f"✅ Found MCP server at: {mcp_server_path}")
    
    # Step 2: Get Alpaca credentials
    print("\n🔑 Step 2: Alpaca API Credentials")
    api_key, secret_key = get_alpaca_credentials()
    
    if not api_key or not secret_key:
        print("❌ Alpaca credentials required for <PERSON> integration")
        return False
    
    # Step 3: Create Claude <PERSON> config
    print("\n🔧 Step 3: Creating Claude Desktop Configuration...")
    config_created = create_claude_config(mcp_server_path, api_key, secret_key)
    
    if not config_created:
        print("❌ Failed to create Claude configuration")
        return False
    
    # Step 4: Setup instructions
    print("\n📚 Step 4: Setup Instructions")
    display_claude_setup_instructions()
    
    print("\n🎉 CLAUDE INTEGRATION SETUP COMPLETE!")
    return True

def locate_mcp_server():
    """Locate the alpaca-mcp-server."""
    # Check integrations directory first
    integrations_path = Path("integrations") / "alpaca-mcp-server"
    if integrations_path.exists() and (integrations_path / "alpaca_mcp_server.py").exists():
        return integrations_path
    
    # Check Downloads directory
    downloads_path = Path.home() / "Downloads" / "alpaca-mcp-server-main"
    if downloads_path.exists() and (downloads_path / "alpaca_mcp_server.py").exists():
        return downloads_path
    
    return None

def get_alpaca_credentials():
    """Get Alpaca API credentials."""
    # Try environment variables first
    api_key = os.getenv('ALPACA_API_KEY')
    secret_key = os.getenv('ALPACA_SECRET_KEY')
    
    if api_key and secret_key:
        print("✅ Found Alpaca credentials in environment variables")
        return api_key, secret_key
    
    # Try config file
    try:
        config_path = Path("config/config.env")
        if config_path.exists():
            with open(config_path, 'r') as f:
                content = f.read()
                if 'ALPACA_API_KEY' in content and 'ALPACA_SECRET_KEY' in content:
                    print("✅ Found Alpaca credentials in config file")
                    # Parse the config file
                    for line in content.split('\n'):
                        if line.startswith('ALPACA_API_KEY'):
                            api_key = line.split('=')[1].strip().strip('"\'')
                        elif line.startswith('ALPACA_SECRET_KEY'):
                            secret_key = line.split('=')[1].strip().strip('"\'')
                    
                    if api_key and secret_key:
                        return api_key, secret_key
    except Exception as e:
        print(f"⚠️ Could not read config file: {e}")
    
    # Ask user for credentials
    print("\n🔑 Alpaca API credentials needed for Claude integration")
    print("You can find these in your Alpaca dashboard under API Keys")
    print("(Paper trading keys are recommended for testing)")
    
    api_key = input("\nEnter Alpaca API Key: ").strip()
    secret_key = input("Enter Alpaca Secret Key: ").strip()
    
    if api_key and secret_key:
        return api_key, secret_key
    else:
        return None, None

def create_claude_config(mcp_server_path, api_key, secret_key):
    """Create Claude Desktop configuration."""
    try:
        # Create Claude config
        config = {
            "mcpServers": {
                "alpaca-totalrecall": {
                    "command": "python",
                    "args": [str(mcp_server_path / "alpaca_mcp_server.py")],
                    "env": {
                        "ALPACA_API_KEY": api_key,
                        "ALPACA_SECRET_KEY": secret_key,
                        "PAPER": "true"
                    }
                }
            }
        }
        
        # Save config for user reference
        config_file = Path("integrations/claude_desktop_config.json")
        config_file.parent.mkdir(exist_ok=True)
        
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Claude Desktop config created: {config_file}")
        
        # Also create .env file for MCP server
        env_file = mcp_server_path / ".env"
        env_content = f"""ALPACA_API_KEY = "{api_key}"
ALPACA_SECRET_KEY = "{secret_key}"
PAPER = true
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print("✅ MCP server environment configured")
        return True
        
    except Exception as e:
        print(f"❌ Config creation failed: {e}")
        return False

def display_claude_setup_instructions():
    """Display Claude Desktop setup instructions."""
    print("\n📋 **CLAUDE DESKTOP SETUP INSTRUCTIONS:**")
    print("\n1️⃣ **Install Claude Desktop:**")
    print("   • Download from: https://claude.ai/download")
    print("   • Install and create account if needed")
    
    print("\n2️⃣ **Configure Claude Desktop:**")
    print("   • Open Claude Desktop")
    print("   • Go to Settings → Developer → Edit Config")
    print("   • Copy contents from: integrations/claude_desktop_config.json")
    print("   • Paste into Claude's config and save")
    
    print("\n3️⃣ **Restart Claude Desktop:**")
    print("   • Close Claude Desktop completely")
    print("   • Reopen Claude Desktop")
    print("   • You should see 'alpaca-totalrecall' server connected")
    
    print("\n4️⃣ **Test the Integration:**")
    print("   • In Claude, try: 'What's my account balance?'")
    print("   • Try: 'Show me my current positions'")
    print("   • Try: 'Get a quote for AAPL'")
    
    print("\n🎯 **NATURAL LANGUAGE TRADING EXAMPLES:**")
    print("   💬 'Buy 10 shares of AAPL at market price'")
    print("   💬 'What are the Greeks for my TSLA options?'")
    print("   💬 'Show me upcoming earnings for tech stocks'")
    print("   💬 'Place a bull call spread on NVDA'")
    print("   💬 'What's my portfolio risk exposure?'")
    print("   💬 'Create a watchlist with AAPL, MSFT, GOOGL'")
    
    print("\n🔄 **INTEGRATION BENEFITS:**")
    print("   ✅ Natural language trading (no command syntax)")
    print("   ✅ Intelligent responses with explanations")
    print("   ✅ Advanced options analysis")
    print("   ✅ Real-time portfolio management")
    print("   ✅ Corporate actions intelligence")
    print("   ✅ Works alongside your TotalRecall system")

def create_comparison_guide():
    """Create a comparison guide for different usage options."""
    guide_content = """# 🤖 TotalRecall + Claude Integration Guide

## 🎯 Usage Options Comparison

### Option 1: Enhanced TotalRecall Chat Only
**What:** Use enhanced commands within your existing TotalRecall interface
**Best For:** Users who prefer their current workflow
**Commands:** 
- "Greeks for AAPL"
- "Portfolio analysis" 
- "Earnings calendar"

### Option 2: Claude Desktop Only
**What:** Natural language trading through Claude Desktop
**Best For:** Users who want conversational trading
**Commands:**
- "Buy 10 shares of AAPL and set a 3% trailing stop"
- "What's my risk exposure and how should I hedge it?"
- "Analyze my TSLA options and suggest adjustments"

### Option 3: Both (Recommended)
**What:** Use both systems for different purposes
**Best For:** Maximum flexibility and capability
**Workflow:**
- **TotalRecall:** Main trading system, TTM analysis, scanning
- **Claude:** Natural language analysis, complex queries, explanations

## 🚀 Recommended Workflow

### Daily Trading Routine:
1. **Morning:** Use TotalRecall TTM scanner for opportunities
2. **Analysis:** Ask Claude for detailed analysis of opportunities
3. **Execution:** Use either system for trade execution
4. **Monitoring:** TotalRecall for real-time monitoring
5. **Review:** Claude for portfolio analysis and insights

### Example Integration:
```
TotalRecall Scanner: "Found A+ TTM setup on AAPL"
↓
Claude Analysis: "Analyze AAPL setup and suggest position size"
↓
Execution: Use either system to place trade
↓
Monitoring: TotalRecall dashboard for real-time tracking
```

## 💡 Best Practices

### Use TotalRecall For:
- TTM squeeze scanning
- Real-time monitoring
- Quick trade execution
- Performance dashboards
- Automated scanning

### Use Claude For:
- Complex analysis questions
- Natural language queries
- Options strategy explanations
- Portfolio optimization advice
- Learning and education

## 🎉 The Ultimate Setup

With both systems, you get:
- **Powerful TTM Analysis** (TotalRecall)
- **Natural Language Intelligence** (Claude)
- **Seamless Integration** (Both work together)
- **Maximum Flexibility** (Use what fits the situation)
"""
    
    guide_file = Path("TOTALRECALL_CLAUDE_INTEGRATION_GUIDE.md")
    with open(guide_file, 'w') as f:
        f.write(guide_content)
    
    print(f"✅ Integration guide created: {guide_file}")

def main():
    """Main setup function."""
    print("🤖 TotalRecall + Claude Desktop Integration")
    print("=" * 50)
    print("This will set up Claude Desktop to work with your alpaca-mcp-server")
    print("for natural language trading alongside your TotalRecall system.\n")
    
    # Run Claude setup
    success = setup_claude_integration()
    
    if success:
        # Create comparison guide
        create_comparison_guide()
        
        print("\n🎉 **SETUP COMPLETE!**")
        print("\n🚀 **You Now Have:**")
        print("✅ Enhanced TotalRecall chat (existing)")
        print("✅ Claude Desktop integration (new)")
        print("✅ Natural language trading capabilities")
        print("✅ Best of both worlds!")
        
        print("\n💡 **Next Steps:**")
        print("1. Install Claude Desktop if you haven't already")
        print("2. Follow the configuration instructions above")
        print("3. Test both systems")
        print("4. Use the integration guide for best practices")
        
        print("\n🎯 **Quick Test:**")
        print("• TotalRecall: 'Greeks for AAPL'")
        print("• Claude: 'What are the Greeks for AAPL and what do they mean?'")
        
    else:
        print("\n❌ Claude setup failed")
        print("💡 Your enhanced TotalRecall system still works normally")
    
    return success

if __name__ == "__main__":
    main()
