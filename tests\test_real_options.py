#!/usr/bin/env python3
"""Test the options scanner with real Alpaca data."""

from options_opportunity_scanner import OptionsOpportunityScanner

def test_real_options_scanner():
    """Test the options scanner with real Alpaca options data."""
    print("🧪 Testing Options Scanner with REAL Alpaca Data")
    print("=" * 60)
    
    # Initialize scanner
    scanner = OptionsOpportunityScanner()
    
    # Test single stock
    test_symbol = "AAPL"
    print(f"\n📊 Testing {test_symbol} with real options data...")
    
    # Test Alpaca options data directly
    print(f"1. Getting real Alpaca options data for {test_symbol}...")
    alpaca_data = scanner._get_alpaca_options_data(test_symbol)
    print(f"   ✅ Real Alpaca Options Found: {len(alpaca_data)}")
    
    if alpaca_data:
        print(f"   📊 Sample real options:")
        for i, option in enumerate(alpaca_data[:5]):
            print(f"     {i+1}. {option['symbol']}")
            print(f"        Strike: ${option['strike']:.2f}, Type: {option['type']}")
            print(f"        Price: ${option['lastPrice']:.2f}, Bid: ${option['bid']:.2f}, Ask: ${option['ask']:.2f}")
            print(f"        IV: {option['impliedVolatility']:.3f}, Delta: {option['delta']:.3f}")
    else:
        print("   ❌ No real options data found!")
        return
    
    # Test full options chain (should use Alpaca now)
    print(f"\n2. Getting full options chain for {test_symbol}...")
    options_chain = scanner._get_options_chain(test_symbol)
    print(f"   ✅ Total Options Found: {len(options_chain)}")
    
    if options_chain:
        print(f"   📊 Sample contracts from chain:")
        for i, contract in enumerate(options_chain[:3]):
            print(f"     {i+1}. {contract['symbol']}")
            print(f"        Strike: ${contract['strike']:.2f}, Type: {contract['type']}")
            print(f"        Price: ${contract['lastPrice']:.2f}")
    
    # Test opportunity evaluation with real data
    print(f"\n3. Evaluating opportunities with real data...")
    stock_price = scanner._get_stock_price(test_symbol)
    print(f"   Stock Price: ${stock_price:.2f}")
    
    if options_chain and stock_price > 0:
        # Evaluate first few options
        opportunities = []
        for option in options_chain[:10]:
            opportunity = scanner._evaluate_option_opportunity(option, stock_price)
            if opportunity:
                opportunities.append(opportunity)
        
        print(f"   ✅ Found {len(opportunities)} valid opportunities")
        
        if opportunities:
            # Sort by score
            opportunities.sort(key=lambda x: x.score, reverse=True)
            
            print(f"\n   🏆 Top 3 Real Opportunities:")
            for i, opp in enumerate(opportunities[:3]):
                print(f"     {i+1}. {opp.symbol}")
                print(f"        Score: {opp.score:.1f}/100")
                print(f"        Strike: ${opp.strike:.2f}, Type: {opp.option_type}")
                print(f"        Price: ${opp.option_price:.2f}")
                print(f"        Reasoning: {opp.reasoning[:100]}...")
    
    print(f"\n✅ Real options test completed!")

def test_validation_with_real_data():
    """Test that real data passes validation."""
    print(f"\n🔍 Testing validation with real data...")
    
    scanner = OptionsOpportunityScanner()
    
    # Get real options data
    alpaca_data = scanner._get_alpaca_options_data("AAPL")
    
    if alpaca_data:
        print(f"   Testing validation on {len(alpaca_data)} real options...")
        
        valid_count = 0
        invalid_count = 0
        
        for option in alpaca_data:
            if scanner._validate_option_data(option):
                valid_count += 1
            else:
                invalid_count += 1
                print(f"   ❌ Invalid option: {option['symbol']} - Price: ${option['lastPrice']:.2f}")
        
        print(f"   ✅ Valid options: {valid_count}")
        print(f"   ❌ Invalid options: {invalid_count}")
        print(f"   📊 Validation rate: {valid_count/(valid_count+invalid_count)*100:.1f}%")
    else:
        print("   ❌ No real data to validate!")

if __name__ == "__main__":
    test_real_options_scanner()
    test_validation_with_real_data()
