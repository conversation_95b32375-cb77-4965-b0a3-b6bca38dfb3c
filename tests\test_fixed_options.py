#!/usr/bin/env python3
"""Test the fixed options system."""

from options_strategies import get_options_strategy_recommendation

def test_fixed_system():
    """Test the fixed options system with actionable results."""
    print("🔧 Testing Fixed Options System")
    print("=" * 50)
    
    test_cases = [
        ("AAPL", "bullish", "moderate"),
        ("SPY", "bullish", "conservative"),
        ("QQQ", "bearish", "moderate"),
        ("TSLA", "neutral", "moderate")
    ]
    
    for symbol, outlook, risk in test_cases:
        print(f"\n📊 Testing {symbol} - {outlook} outlook, {risk} risk:")
        try:
            result = get_options_strategy_recommendation(symbol, outlook, risk)
            
            if "error" not in result:
                print(f"   ✅ Strategy: {result['strategy']}")
                print(f"   📈 Max Profit: ${result['max_profit']:.2f}")
                print(f"   📉 Max Loss: ${result['max_loss']:.2f}")
                print(f"   ⚖️  Risk/Reward: {result['risk_reward_ratio']:.2f}")
                print(f"   🎯 Recommendation: {result['recommendation']}")
            else:
                print(f"   ❌ Error: {result['error']}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

if __name__ == "__main__":
    test_fixed_system()
    print("\n🎉 Fixed system test complete!")
