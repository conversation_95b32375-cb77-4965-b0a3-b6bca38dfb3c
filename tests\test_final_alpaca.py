#!/usr/bin/env python3
"""Final comprehensive test of the updated Alpaca options system."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from options_opportunity_scanner import OptionsOpportunityScanner

def test_alpaca_integration():
    """Test the complete Alpaca options integration."""
    print("🧪 FINAL TEST: Alpaca Options Integration")
    print("=" * 60)

    # Initialize scanner
    scanner = OptionsOpportunityScanner()

    # Test symbols
    test_symbols = ["AAPL", "TSLA", "SPY"]

    for symbol in test_symbols:
        print(f"\n📊 Testing {symbol}...")
        print("-" * 40)

        # Test 1: Get stock price
        stock_price = scanner._get_stock_price(symbol)
        print(f"1. Stock Price: ${stock_price:.2f}")

        if stock_price <= 0:
            print(f"   ❌ Invalid stock price for {symbol}")
            continue

        # Test 2: Get Alpaca options data
        print(f"2. Getting Alpaca options data...")
        alpaca_data = scanner._get_alpaca_options_data(symbol)
        print(f"   ✅ Found {len(alpaca_data)} real options")

        if not alpaca_data:
            print(f"   ❌ No options data for {symbol}")
            continue

        # Test 3: Validate options data
        print(f"3. Validating options data...")
        valid_count = 0
        invalid_count = 0
        price_issues = []

        for option in alpaca_data:
            if scanner._validate_option_data(option):
                valid_count += 1
            else:
                invalid_count += 1
                price = option.get('lastPrice', 0)
                if price > 1000 or price < 0:
                    price_issues.append(f"{option['symbol']}: ${price:.2f}")

        print(f"   ✅ Valid: {valid_count}, ❌ Invalid: {invalid_count}")
        if price_issues:
            print(f"   🚨 Price issues: {price_issues[:3]}")

        # Test 4: Show sample real options
        print(f"4. Sample real options:")
        for i, option in enumerate(alpaca_data[:3]):
            print(f"   {i+1}. {option['symbol']}")
            print(f"      Strike: ${option['strike']:.2f}, Type: {option['type']}")
            print(f"      Price: ${option['lastPrice']:.2f}")
            print(f"      Bid: ${option['bid']:.2f}, Ask: ${option['ask']:.2f}")
            print(f"      IV: {option['impliedVolatility']:.3f}")

        # Test 5: Evaluate opportunities
        print(f"5. Evaluating opportunities...")
        opportunities = []

        for option in alpaca_data[:20]:  # Test first 20 options
            opportunity = scanner._evaluate_option_opportunity(option, stock_price)
            if opportunity:
                opportunities.append(opportunity)

        print(f"   ✅ Found {len(opportunities)} valid opportunities")

        if opportunities:
            # Sort by score
            opportunities.sort(key=lambda x: x.score, reverse=True)

            print(f"   🏆 Top opportunity:")
            top = opportunities[0]
            print(f"      {top.symbol} - Score: {top.score:.1f}/100")
            print(f"      Strike: ${top.strike:.2f}, Price: ${top.option_price:.2f}")
            print(f"      Reasoning: {top.reasoning[:80]}...")

        print(f"   ✅ {symbol} test completed!")

def test_no_synthetic_data():
    """Verify that no synthetic data is being generated."""
    print(f"\n🔍 Testing: No Synthetic Data Generation")
    print("-" * 50)

    scanner = OptionsOpportunityScanner()

    # Test with a symbol that might not have options
    test_symbol = "ZZZZ"  # Unlikely to have real options

    print(f"Testing {test_symbol} (should have no options)...")
    options_data = scanner._get_options_chain(test_symbol)

    if not options_data:
        print("✅ PASS: No synthetic data generated for invalid symbol")
    else:
        print(f"❌ FAIL: Generated {len(options_data)} options for invalid symbol")
        print("   This suggests synthetic data is still being created!")

def test_price_validation():
    """Test that price validation is working correctly."""
    print(f"\n🔍 Testing: Price Validation System")
    print("-" * 50)

    scanner = OptionsOpportunityScanner()

    # Test with realistic option data
    realistic_option = {
        "symbol": "AAPL250117C00150000",
        "strike": 150.0,
        "expiration": "2025-01-17",
        "type": "call",
        "lastPrice": 5.50,
        "bid": 5.40,
        "ask": 5.60,
        "volume": 100,
        "openInterest": 1000,
        "impliedVolatility": 0.25,
        "delta": 0.6,
        "underlying_symbol": "AAPL"
    }

    # Test with unrealistic option data
    unrealistic_option = {
        "symbol": "AAPL250117C00150000",
        "strike": 150.0,
        "expiration": "2025-01-17",
        "type": "call",
        "lastPrice": 5000.0,  # Unrealistic price
        "bid": 4999.0,
        "ask": 5001.0,
        "volume": 100,
        "openInterest": 1000,
        "impliedVolatility": 0.25,
        "delta": 0.6,
        "underlying_symbol": "AAPL"
    }

    # Need to provide stock price for validation
    test_stock_price = 150.0  # AAPL stock price for testing
    realistic_valid = scanner._validate_option_data(realistic_option, test_stock_price)
    unrealistic_valid = scanner._validate_option_data(unrealistic_option, test_stock_price)

    print(f"Realistic option valid: {realistic_valid}")
    print(f"Unrealistic option valid: {unrealistic_valid}")

    if realistic_valid and not unrealistic_valid:
        print("✅ PASS: Price validation working correctly")
    else:
        print("❌ FAIL: Price validation not working properly")

if __name__ == "__main__":
    test_alpaca_integration()
    test_no_synthetic_data()
    test_price_validation()

    print(f"\n🎉 FINAL TEST COMPLETED!")
    print("=" * 60)
