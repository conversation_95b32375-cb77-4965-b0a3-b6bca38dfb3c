#!/usr/bin/env python3
"""Simple test of multi-leg options functionality."""

def test_multileg_import():
    """Test importing the multi-leg classes."""
    try:
        from options_strategies import OptionsLeg, MultiLegOrder, OptionsStrategies
        print("✅ Multi-leg classes imported successfully")
        
        # Test creating a simple leg
        leg = OptionsLeg(
            symbol="AAPL240315C00150000",
            ratio_qty=1,
            side="buy",
            position_intent="buy_to_open"
        )
        print(f"✅ Created OptionsLeg: {leg.symbol}")
        
        # Test creating a multi-leg order
        order = MultiLegOrder(
            order_class="mleg",
            qty=1,
            type="limit",
            limit_price=5.50,
            legs=[leg]
        )
        print(f"✅ Created MultiLegOrder with {len(order.legs)} legs")
        
        # Test creating options strategies instance
        options = OptionsStrategies()
        print("✅ OptionsStrategies instance created")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_simple_strategy():
    """Test a simple strategy."""
    try:
        from options_strategies import get_options_strategy_recommendation
        
        result = get_options_strategy_recommendation("AAPL", "bullish", "moderate")
        print(f"✅ Strategy recommendation: {result.get('strategy', 'Unknown')}")
        print(f"   Max Profit: ${result.get('max_profit', 0):.2f}")
        print(f"   Max Loss: ${result.get('max_loss', 0):.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Strategy test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Simple Multi-Leg Test")
    print("=" * 30)
    
    success1 = test_multileg_import()
    success2 = test_simple_strategy()
    
    if success1 and success2:
        print("\n🎉 Multi-leg functionality is working!")
    else:
        print("\n❌ Some tests failed")
