#!/usr/bin/env python3
"""Verify Black-Scholes calculations against known correct formulas."""

import math
import numpy as np
from scipy.stats import norm

def black_scholes_call(S, K, T, r, sigma):
    """Standard Black-Scholes call option pricing formula."""
    d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
    d2 = d1 - sigma * math.sqrt(T)
    
    call_price = S * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
    return call_price, d1, d2

def black_scholes_put(S, K, T, r, sigma):
    """Standard Black-Scholes put option pricing formula."""
    d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
    d2 = d1 - sigma * math.sqrt(T)
    
    put_price = K * math.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
    return put_price, d1, d2

def calculate_greeks(S, K, T, r, sigma, option_type="call"):
    """Calculate all Greeks using correct formulas."""
    d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
    d2 = d1 - sigma * math.sqrt(T)
    
    # Standard normal PDF and CDF
    N_d1 = norm.cdf(d1)
    N_d2 = norm.cdf(d2)
    n_d1 = norm.pdf(d1)
    
    if option_type == "call":
        # Call Greeks
        delta = N_d1
        theta = (-S * n_d1 * sigma / (2 * math.sqrt(T)) - r * K * math.exp(-r * T) * N_d2) / 365
        rho = K * T * math.exp(-r * T) * N_d2 / 100
    else:
        # Put Greeks  
        delta = N_d1 - 1
        theta = (-S * n_d1 * sigma / (2 * math.sqrt(T)) + r * K * math.exp(-r * T) * (1 - N_d2)) / 365
        rho = -K * T * math.exp(-r * T) * (1 - N_d2) / 100
    
    # Gamma and Vega are same for calls and puts
    gamma = n_d1 / (S * sigma * math.sqrt(T))
    vega = S * n_d1 * math.sqrt(T) / 100
    
    return {
        "delta": delta,
        "gamma": gamma, 
        "theta": theta,
        "vega": vega,
        "rho": rho
    }

def test_known_values():
    """Test against known option values."""
    print("🧮 Verifying Black-Scholes Calculations")
    print("=" * 50)
    
    # Test case: SPY at $590, $600 call, 30 days, 5% rate, 25% vol
    S = 590.0  # Current price
    K = 600.0  # Strike price
    T = 30/365.0  # 30 days to expiration
    r = 0.05  # 5% risk-free rate
    sigma = 0.25  # 25% volatility
    
    print(f"Test Parameters:")
    print(f"Stock Price: ${S}")
    print(f"Strike Price: ${K}")
    print(f"Time to Expiry: {T:.4f} years ({30} days)")
    print(f"Risk-free Rate: {r:.1%}")
    print(f"Volatility: {sigma:.1%}")
    print()
    
    # Calculate call option
    call_price, d1, d2 = black_scholes_call(S, K, T, r, sigma)
    print(f"📈 CALL OPTION:")
    print(f"Theoretical Price: ${call_price:.2f}")
    print(f"d1: {d1:.4f}")
    print(f"d2: {d2:.4f}")
    
    # Calculate put option
    put_price, d1, d2 = black_scholes_put(S, K, T, r, sigma)
    print(f"\n📉 PUT OPTION:")
    print(f"Theoretical Price: ${put_price:.2f}")
    
    # Verify put-call parity: C - P = S - K*e^(-rT)
    parity_check = call_price - put_price
    expected_parity = S - K * math.exp(-r * T)
    print(f"\n🔍 PUT-CALL PARITY CHECK:")
    print(f"C - P = ${parity_check:.2f}")
    print(f"S - K*e^(-rT) = ${expected_parity:.2f}")
    print(f"Difference: ${abs(parity_check - expected_parity):.6f}")
    print(f"✅ Parity Check: {'PASS' if abs(parity_check - expected_parity) < 0.01 else 'FAIL'}")
    
    # Calculate Greeks
    call_greeks = calculate_greeks(S, K, T, r, sigma, "call")
    put_greeks = calculate_greeks(S, K, T, r, sigma, "put")
    
    print(f"\n📊 CALL GREEKS:")
    for greek, value in call_greeks.items():
        print(f"{greek.capitalize()}: {value:.4f}")
    
    print(f"\n📊 PUT GREEKS:")
    for greek, value in put_greeks.items():
        print(f"{greek.capitalize()}: {value:.4f}")
    
    # Sanity checks
    print(f"\n🔍 SANITY CHECKS:")
    print(f"Call Delta (0 to 1): {0 <= call_greeks['delta'] <= 1}")
    print(f"Put Delta (-1 to 0): {-1 <= put_greeks['delta'] <= 0}")
    print(f"Gamma > 0: {call_greeks['gamma'] > 0}")
    print(f"Theta < 0: {call_greeks['theta'] < 0}")
    print(f"Vega > 0: {call_greeks['vega'] > 0}")

def test_edge_cases():
    """Test edge cases and boundary conditions."""
    print(f"\n🧪 Testing Edge Cases")
    print("=" * 30)
    
    # Deep ITM call (should have delta near 1)
    S, K = 100, 80
    T, r, sigma = 0.25, 0.05, 0.25
    greeks = calculate_greeks(S, K, T, r, sigma, "call")
    print(f"Deep ITM Call Delta: {greeks['delta']:.4f} (should be near 1.0)")
    
    # Deep OTM call (should have delta near 0)
    S, K = 100, 120
    greeks = calculate_greeks(S, K, T, r, sigma, "call")
    print(f"Deep OTM Call Delta: {greeks['delta']:.4f} (should be near 0.0)")
    
    # ATM option (should have delta near 0.5)
    S, K = 100, 100
    greeks = calculate_greeks(S, K, T, r, sigma, "call")
    print(f"ATM Call Delta: {greeks['delta']:.4f} (should be near 0.5)")

def compare_with_our_implementation():
    """Compare with our implementation in options_strategies.py."""
    print(f"\n🔄 Comparing with Our Implementation")
    print("=" * 40)
    
    try:
        from options_strategies import OptionsStrategies
        
        options = OptionsStrategies()
        
        # Test our Black-Scholes implementation
        S, K, T_days, option_type = 590, 600, 30, "call"
        
        # Create mock contract data
        contract_data = {
            "strike": K,
            "type": option_type,
            "expiration": "2025-02-15",  # ~30 days from now
            "underlying_symbol": "SPY",
            "impliedVolatility": 0.25
        }
        
        # Test our Greeks calculation
        our_greeks = options._calculate_greeks(contract_data)
        print(f"Our Greeks: {our_greeks}")
        
        # Test our pricing
        our_price = options._black_scholes_price(S, K, "2025-02-15", option_type)
        print(f"Our Price: ${our_price:.2f}")
        
        # Compare with standard calculation
        standard_price, _, _ = black_scholes_call(S, K, 30/365, 0.05, 0.25)
        standard_greeks = calculate_greeks(S, K, 30/365, 0.05, 0.25, option_type)
        
        print(f"Standard Price: ${standard_price:.2f}")
        print(f"Standard Greeks: {standard_greeks}")
        
        print(f"\n📊 Comparison:")
        print(f"Price Difference: ${abs(our_price - standard_price):.4f}")
        
    except Exception as e:
        print(f"❌ Error testing our implementation: {e}")

if __name__ == "__main__":
    test_known_values()
    test_edge_cases()
    compare_with_our_implementation()
    
    print(f"\n✅ Black-Scholes Verification Complete!")
    print("If all checks pass, our calculations are mathematically correct.")
