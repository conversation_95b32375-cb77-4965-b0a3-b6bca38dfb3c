#!/usr/bin/env python3
"""Test the correct Alpaca options chain endpoint."""

import requests
import json

def test_alpaca_options_chain():
    """Test Alpaca options chain API with the correct endpoint."""
    
    # Test with AAPL
    symbol = "AAPL"
    url = f"https://data.sandbox.alpaca.markets/v1beta1/options/snapshots/{symbol}"
    
    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": "PK43FUDB28UZYZ87BT2V",
        "APCA-API-SECRET-KEY": "ICvalUW5EpIlQnInIidzIoDXrqQMdJ2CZJCD7RKg"
    }
    
    print(f"🔍 Testing Alpaca Options Chain for {symbol}...")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS! Got JSON response")
            print(f"📊 Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            
            if isinstance(data, dict) and "snapshots" in data:
                snapshots = data["snapshots"]
                print(f"📊 Number of option contracts: {len(snapshots)}")
                
                if snapshots:
                    # Show first few option contracts
                    symbols = list(snapshots.keys())[:5]
                    print(f"📊 Sample option symbols: {symbols}")
                    
                    # Show detailed data for first contract
                    if symbols:
                        first_symbol = symbols[0]
                        first_snapshot = snapshots[first_symbol]
                        print(f"\n📊 Detailed data for {first_symbol}:")
                        print(f"   Snapshot keys: {list(first_snapshot.keys())}")
                        
                        # Show latest trade
                        latest_trade = first_snapshot.get("latestTrade", {})
                        if latest_trade:
                            print(f"   Latest Trade: Price=${latest_trade.get('p', 0):.2f}, Size={latest_trade.get('s', 0)}")
                        
                        # Show latest quote
                        latest_quote = first_snapshot.get("latestQuote", {})
                        if latest_quote:
                            print(f"   Latest Quote: Bid=${latest_quote.get('bp', 0):.2f}, Ask=${latest_quote.get('ap', 0):.2f}")
                        
                        # Show greeks
                        greeks = first_snapshot.get("greeks", {})
                        if greeks:
                            print(f"   Greeks: Delta={greeks.get('delta', 0):.3f}, IV={greeks.get('iv', 0):.3f}")
                        
                        # Test symbol parsing
                        print(f"\n🧪 Testing symbol parser on: {first_symbol}")
                        import re
                        match = re.search(r'(\d{6}[CP])', first_symbol)
                        if match:
                            date_cp_start = match.start()
                            underlying = first_symbol[:date_cp_start]
                            date_cp = first_symbol[date_cp_start:date_cp_start+7]
                            strike_part = first_symbol[date_cp_start+7:date_cp_start+15]
                            
                            print(f"   Underlying: {underlying}")
                            print(f"   Date+Type: {date_cp}")
                            print(f"   Strike: {strike_part} -> ${float(strike_part)/1000:.2f}")
                        else:
                            print("   Could not parse symbol format")
                else:
                    print("📊 No option contracts found")
            else:
                print(f"📊 Response structure: {data}")
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Error response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_multiple_symbols():
    """Test multiple symbols to see data availability."""
    symbols = ["AAPL", "TSLA", "SPY", "QQQ"]
    
    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": "PK43FUDB28UZYZ87BT2V",
        "APCA-API-SECRET-KEY": "ICvalUW5EpIlQnInIidzIoDXrqQMdJ2CZJCD7RKg"
    }
    
    print(f"\n🔍 Testing multiple symbols...")
    
    for symbol in symbols:
        url = f"https://data.sandbox.alpaca.markets/v1beta1/options/snapshots/{symbol}"
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data and "snapshots" in data:
                    count = len(data["snapshots"])
                    print(f"   {symbol}: ✅ {count} contracts")
                else:
                    print(f"   {symbol}: ⚠️ No snapshots data")
            else:
                print(f"   {symbol}: ❌ Error {response.status_code}")
                
        except Exception as e:
            print(f"   {symbol}: ❌ Exception: {e}")

if __name__ == "__main__":
    test_alpaca_options_chain()
    test_multiple_symbols()
