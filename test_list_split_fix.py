#!/usr/bin/env python3
"""
Test List Split Fix
Verify that the 'list object has no attribute split' error is fixed
"""
import sys
import os

# Add paths
sys.path.insert(0, os.getcwd())
sys.path.insert(0, os.path.join(os.getcwd(), 'gui'))

def test_parse_scan_results():
    """Test the parse_scan_results method with different input types"""
    print("🧪 Testing parse_scan_results Method")
    print("-" * 35)
    
    try:
        # Import the interface class
        from tkinter_trading_interface import TradingInterface
        import tkinter as tk
        
        # Create interface instance (it creates its own root)
        interface = TradingInterface()
        interface.root.withdraw()  # Hide the window
        
        # Test 1: Empty list (this was causing the error)
        print("Test 1: Empty list input")
        result = interface.parse_scan_results([], "B", 10)
        print(f"✅ Empty list handled: {len(result)} results")
        
        # Test 2: List with strings
        print("\nTest 2: List with strings")
        test_list = ["📈 AAPL (1hour) - Grade A+ (95%)", "Entry: $150.00 | Stop: $145.00 | Target: $158.00"]
        result = interface.parse_scan_results(test_list, "B", 10)
        print(f"✅ String list handled: {len(result)} results")
        
        # Test 3: Regular string (normal case)
        print("\nTest 3: Regular string input")
        test_string = """📈 PLTR (15min) - Grade A (88%)
Entry: $18.45 | Stop: $17.90 | Target: $19.85"""
        result = interface.parse_scan_results(test_string, "B", 10)
        print(f"✅ String input handled: {len(result)} results")
        
        # Test 4: Non-string, non-list input
        print("\nTest 4: Number input")
        result = interface.parse_scan_results(123, "B", 10)
        print(f"✅ Number input handled: {len(result)} results")
        
        # Test 5: None input
        print("\nTest 5: None input")
        result = interface.parse_scan_results(None, "B", 10)
        print(f"✅ None input handled: {len(result)} results")

        interface.root.destroy()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    return True

def test_fallback_scanners():
    """Test that fallback scanners return strings, not lists"""
    print("\n🔍 Testing Fallback Scanner Functions")
    print("-" * 35)
    
    try:
        # Import the fallback functions
        sys.path.insert(0, os.path.join(os.getcwd(), 'gui'))
        from tkinter_trading_interface import run_proper_ttm_scan, run_ttm_squeeze_scan
        
        # Test run_proper_ttm_scan
        result = run_proper_ttm_scan()
        print(f"✅ run_proper_ttm_scan returns: {type(result).__name__}")
        assert isinstance(result, str), "Should return string, not list"
        
        # Test run_ttm_squeeze_scan
        result = run_ttm_squeeze_scan()
        print(f"✅ run_ttm_squeeze_scan returns: {type(result).__name__}")
        assert isinstance(result, str), "Should return string, not list"
        
        print("✅ All fallback scanners return strings")
        
    except Exception as e:
        print(f"❌ Fallback scanner test failed: {e}")
        return False
    
    return True

def test_scan_button_simulation():
    """Simulate clicking the scan button to test the full flow"""
    print("\n🖱️  Testing Scan Button Flow")
    print("-" * 35)
    
    try:
        # Import the interface class
        from tkinter_trading_interface import TradingInterface
        import tkinter as tk
        
        # Create interface instance (it creates its own root)
        interface = TradingInterface()
        interface.root.withdraw()  # Hide the window
        
        # Simulate the scan flow that was causing the error
        print("Simulating scan button click...")
        
        # This should not crash with "list object has no attribute 'split'"
        from tkinter_trading_interface import run_proper_ttm_scan
        scan_result = run_proper_ttm_scan()
        
        print(f"✅ Scanner returned: {type(scan_result).__name__}")
        
        # Parse the results (this was where the error occurred)
        results = interface.parse_scan_results(scan_result, "B", 10)
        print(f"✅ Parse results successful: {len(results)} results")

        interface.root.destroy()
        
    except Exception as e:
        print(f"❌ Scan button simulation failed: {e}")
        return False
    
    return True

def main():
    """Main test function"""
    print("🔧 LIST SPLIT ERROR FIX TEST")
    print("🖥️  Testing 'list object has no attribute split' Fix")
    print("=" * 55)
    
    tests = [
        test_parse_scan_results,
        test_fallback_scanners,
        test_scan_button_simulation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n🎯 **TEST RESULTS: {passed}/{total} PASSED**")
    
    if passed == total:
        print("\n🎉 **ALL TESTS PASSED!**")
        print("\n✅ **LIST SPLIT ERROR FIXED:**")
        print("   • parse_scan_results handles lists properly")
        print("   • Fallback scanners return strings")
        print("   • Scan button flow works correctly")
        print("   • No more 'list object has no attribute split' errors")
        
        print("\n🚀 **INTERFACE BUTTONS SHOULD NOW WORK:**")
        print("   • TTM Scanner button works")
        print("   • S&P 500 Scanner button works")
        print("   • No more scan result errors")
        print("   • All parsing functions stable")
    else:
        print(f"\n⚠️  **{total - passed} TESTS FAILED**")
        print("   • Some issues may remain")
        print("   • Check error messages above")

if __name__ == "__main__":
    main()
