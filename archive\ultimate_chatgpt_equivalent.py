"""Ultimate ChatGPT Equivalent System

This system can do EVERYTHING ChatGPT can do:
- General conversations and reasoning
- Creative writing and storytelling
- Code generation and debugging
- Mathematical calculations and analysis
- Educational explanations
- Problem solving and planning
- Research and information synthesis
- Language translation
- Content creation
- AND all the specialized trading capabilities

The goal is to be indistinguishable from ChatGPT while having superior trading intelligence.
"""
from __future__ import annotations

import json
import re
import math
import random
from datetime import datetime
from typing import Dict, List, Optional, Any
from openai import OpenAI

from config import get_api_key
from logger_util import info, warning

# Import all trading capabilities
from chat_core import TOOLS
from universal_market_intelligence import answer_any_market_question


class UltimateChatGPTEquivalent:
    """ChatGPT equivalent with enhanced trading intelligence."""
    
    def __init__(self):
        self.client = OpenAI(api_key=get_api_key("OPENAI_API_KEY"))
        self.conversation_history = []
        self.trading_tools = TOOLS
        
        # Enhanced system prompt that covers everything
        self.system_prompt = self._build_comprehensive_system_prompt()
    
    def _build_comprehensive_system_prompt(self) -> str:
        """Build comprehensive system prompt covering all capabilities."""
        return """You are an advanced AI assistant with comprehensive capabilities equivalent to ChatGPT, plus specialized trading expertise.

CORE CAPABILITIES:
• General conversation and reasoning
• Creative writing, storytelling, and content creation
• Code generation, debugging, and programming help
• Mathematical calculations and problem solving
• Educational explanations and tutoring
• Research, analysis, and information synthesis
• Language translation and linguistic tasks
• Planning, organization, and productivity
• Scientific and technical discussions
• Philosophical and ethical reasoning

SPECIALIZED TRADING EXPERTISE:
• Real-time market analysis and trading strategies
• TTM Squeeze detection and breakout prediction
• Dynamic stop loss management with ATR/momentum
• Insider trading and institutional flow analysis
• Options strategies and risk management
• Earnings analysis and analyst recommendations
• Technical analysis and chart pattern recognition
• Portfolio optimization and position sizing
• Economic calendar and market event tracking
• Universal market intelligence across all asset classes

INTERACTION STYLE:
• Be helpful, harmless, and honest
• Provide detailed, accurate, and well-reasoned responses
• Use appropriate tone for the context (professional for trading, casual for general chat)
• Ask clarifying questions when needed
• Offer multiple perspectives and solutions
• Be creative and engaging while remaining factual
• Adapt communication style to user preferences

TRADING FUNCTION USAGE:
When users ask trading-related questions, use the appropriate specialized functions:
- Market analysis: analyze_stock_comprehensive()
- Profit planning: execute_profit_target()
- TTM Squeeze: scan_ttm_watchlist()
- Complex questions: answer_any_question()
- Stop loss management: update_stop_loss()

For non-trading questions, respond naturally like ChatGPT without using functions.
"""
    
    def _is_trading_related(self, message: str) -> bool:
        """Determine if a message is trading/finance related."""
        trading_keywords = [
            # Market terms
            'stock', 'stocks', 'market', 'trading', 'trade', 'invest', 'investment',
            'portfolio', 'profit', 'loss', 'buy', 'sell', 'price', 'ticker', 'symbol',
            
            # Technical analysis
            'chart', 'technical', 'support', 'resistance', 'trend', 'breakout',
            'squeeze', 'ttm', 'bollinger', 'moving average', 'rsi', 'macd',
            
            # Financial terms
            'earnings', 'revenue', 'eps', 'pe ratio', 'dividend', 'yield',
            'financial', 'balance sheet', 'cash flow', 'debt', 'equity',
            
            # Options and derivatives
            'option', 'options', 'call', 'put', 'strike', 'expiration',
            'volatility', 'implied volatility', 'delta', 'gamma', 'theta',
            
            # Market participants
            'insider', 'institutional', 'analyst', 'upgrade', 'downgrade',
            'recommendation', 'price target', 'sec filing', 'ipo',
            
            # Economic terms
            'fed', 'interest rate', 'inflation', 'gdp', 'unemployment',
            'economic', 'monetary policy', 'fiscal policy'
        ]
        
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in trading_keywords)
    
    def _determine_trading_function(self, message: str) -> Optional[str]:
        """Determine which trading function to use based on the message."""
        message_lower = message.lower()
        
        # Profit target requests
        if any(phrase in message_lower for phrase in ['make me', 'profit', 'target profit', 'trading plan']):
            return 'execute_profit_target'
        
        # TTM Squeeze requests
        if any(phrase in message_lower for phrase in ['ttm', 'squeeze', 'breakout', 'watchlist', 'before breakout']):
            return 'scan_ttm_watchlist'
        
        # Stock analysis requests
        if any(phrase in message_lower for phrase in ['analyze', 'analysis of', 'tell me about']) and any(phrase in message_lower for phrase in ['stock', 'company']):
            return 'analyze_stock_comprehensive'
        
        # Stop loss requests
        if any(phrase in message_lower for phrase in ['stop loss', 'update stop', 'adjust stop']):
            return 'update_stop_loss'
        
        # Complex/general trading questions
        if any(phrase in message_lower for phrase in ['insider', 'earnings', 'analyst', 'unusual volume', 'sec filing', 'institutional']):
            return 'answer_any_question'
        
        # Market quote requests
        if any(phrase in message_lower for phrase in ['price of', 'quote for', 'current price']):
            return 'get_market_quote'
        
        return None
    
    def _extract_parameters(self, message: str, function_name: str) -> Dict:
        """Extract parameters for trading functions from the message."""
        params = {}
        
        if function_name == 'execute_profit_target':
            # Extract profit amount
            profit_match = re.search(r'\$?(\d+(?:\.\d+)?)', message)
            if profit_match:
                params['target_profit'] = float(profit_match.group(1))
            else:
                params['target_profit'] = 50  # Default
        
        elif function_name == 'analyze_stock_comprehensive':
            # Extract stock symbol
            symbol_match = re.search(r'\b([A-Z]{1,5})\b', message.upper())
            if symbol_match:
                params['symbol'] = symbol_match.group(1)
        
        elif function_name == 'get_market_quote':
            # Extract stock symbol
            symbol_match = re.search(r'\b([A-Z]{1,5})\b', message.upper())
            if symbol_match:
                params['symbol'] = symbol_match.group(1)
        
        elif function_name == 'answer_any_question':
            params['question'] = message
        
        elif function_name == 'update_stop_loss':
            # Extract symbol, entry price, current stop
            symbol_match = re.search(r'\b([A-Z]{1,5})\b', message.upper())
            if symbol_match:
                params['symbol'] = symbol_match.group(1)
            
            # Extract prices (this is simplified - in practice you'd want more robust parsing)
            prices = re.findall(r'\$?(\d+(?:\.\d+)?)', message)
            if len(prices) >= 2:
                params['entry_price'] = float(prices[0])
                params['current_stop'] = float(prices[1])
        
        return params
    
    def _call_trading_function(self, function_name: str, params: Dict) -> str:
        """Call the appropriate trading function."""
        try:
            if function_name in self.trading_tools:
                function = self.trading_tools[function_name]['function']
                
                # Call function with parameters
                if params:
                    result = function(**params)
                else:
                    result = function()
                
                # Format result
                if isinstance(result, dict):
                    return json.dumps(result, indent=2)
                else:
                    return str(result)
            else:
                return f"Trading function '{function_name}' not available."
                
        except Exception as e:
            return f"Error calling trading function: {str(e)}"
    
    def _generate_general_response(self, message: str) -> str:
        """Generate response for general (non-trading) questions using GPT."""
        try:
            # Add message to conversation history
            self.conversation_history.append({"role": "user", "content": message})
            
            # Keep conversation history manageable (last 20 messages)
            if len(self.conversation_history) > 20:
                self.conversation_history = self.conversation_history[-20:]
            
            # Create messages for API call
            messages = [
                {"role": "system", "content": self.system_prompt}
            ] + self.conversation_history
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=messages,
                temperature=0.7,
                max_tokens=2000
            )
            
            assistant_response = response.choices[0].message.content
            
            # Add assistant response to history
            self.conversation_history.append({"role": "assistant", "content": assistant_response})
            
            return assistant_response
            
        except Exception as e:
            return f"I apologize, but I encountered an error: {str(e)}. Please try again."
    
    def chat(self, message: str) -> str:
        """Main chat function that handles everything like ChatGPT."""
        info(f"💬 Processing message: {message[:100]}...")
        
        # Check if this is trading-related
        if self._is_trading_related(message):
            info("🎯 Detected trading-related question")
            
            # Determine which trading function to use
            function_name = self._determine_trading_function(message)
            
            if function_name:
                # Extract parameters and call trading function
                params = self._extract_parameters(message, function_name)
                trading_result = self._call_trading_function(function_name, params)
                
                # Enhance the trading result with natural language explanation
                enhanced_prompt = f"""
                The user asked: "{message}"
                
                I used specialized trading analysis and got this result:
                {trading_result}
                
                Please provide a natural, conversational response that:
                1. Explains the trading analysis in easy-to-understand terms
                2. Highlights the key insights and actionable information
                3. Adds context and recommendations
                4. Maintains a helpful, professional tone
                
                Make it sound natural and conversational, not like raw data output.
                """
                
                return self._generate_general_response(enhanced_prompt)
            else:
                # General trading question - use universal intelligence
                trading_result = answer_any_market_question(message)
                
                enhanced_prompt = f"""
                The user asked: "{message}"
                
                I analyzed this using comprehensive market intelligence:
                {trading_result}
                
                Please provide a natural, conversational response that explains this market analysis clearly.
                """
                
                return self._generate_general_response(enhanced_prompt)
        else:
            # Non-trading question - handle like ChatGPT
            info("💭 Processing general conversation")
            return self._generate_general_response(message)
    
    def reset_conversation(self):
        """Reset conversation history."""
        self.conversation_history = []
        info("🔄 Conversation history reset")


# Global instance
_chatbot = None

def get_ultimate_chatbot() -> UltimateChatGPTEquivalent:
    """Get the global chatbot instance."""
    global _chatbot
    if _chatbot is None:
        _chatbot = UltimateChatGPTEquivalent()
    return _chatbot


def ultimate_chat(message: str) -> str:
    """Main chat function - equivalent to ChatGPT but with trading superpowers."""
    chatbot = get_ultimate_chatbot()
    return chatbot.chat(message)


if __name__ == "__main__":
    print("🤖 ULTIMATE CHATGPT EQUIVALENT WITH TRADING SUPERPOWERS")
    print("=" * 60)
    print("This chatbot can do EVERYTHING ChatGPT can do, plus advanced trading analysis!")
    print("Try asking about anything - general questions, coding, creative writing, or trading!")
    print("=" * 60)
    
    # Interactive chat loop
    chatbot = UltimateChatGPTEquivalent()
    
    while True:
        try:
            user_input = input("\n💬 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break
            
            if user_input.lower() == 'reset':
                chatbot.reset_conversation()
                print("🔄 Conversation reset!")
                continue
            
            if not user_input:
                continue
            
            print("\n🤖 Assistant: ", end="")
            response = chatbot.chat(user_input)
            print(response)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            continue
