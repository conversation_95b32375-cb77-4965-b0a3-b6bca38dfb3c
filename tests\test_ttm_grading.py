"""Test TTM Squeeze Grading System

Quick test to verify the A-F grading system is working correctly
and show A/B grade opportunities first.
"""
from __future__ import annotations

from ttm_squeeze_scanner import scan_ttm_squeeze_opportunities
from advanced_ttm_squeeze_scanner import run_ttm_squeeze_scan
from logger_util import info

def test_grading_system():
    """Test the TTM squeeze grading system."""
    print("🎯 Testing TTM Squeeze Grading System")
    print("=" * 50)
    
    # Test the simple scanner with grading
    print("\n📊 Testing Simple Scanner with A-F Grading:")
    try:
        results = scan_ttm_squeeze_opportunities(min_grade='B', max_results=20)
        
        if 'top_opportunities' in results:
            opportunities = results['top_opportunities']
            
            # Sort by grade (A+ > A > B > C > D > F)
            grade_order = {'A+': 0, 'A': 1, 'B': 2, 'C': 3, 'D': 4, 'F': 5}
            opportunities.sort(key=lambda x: grade_order.get(x.get('grade', 'F'), 5))
            
            print(f"\n🔍 Found {len(opportunities)} opportunities:")
            
            # Show A/B grades first
            high_grade = [opp for opp in opportunities if opp.get('grade', 'F') in ['A+', 'A', 'B']]
            other_grade = [opp for opp in opportunities if opp.get('grade', 'F') not in ['A+', 'A', 'B']]
            
            if high_grade:
                print("\n🚨 HIGH GRADE OPPORTUNITIES (A/B):")
                for opp in high_grade:
                    print(f"  📈 {opp.get('symbol', 'N/A')} - Grade {opp.get('grade', 'N/A')} "
                          f"({opp.get('confidence', 0)*100:.1f}%)")
                    print(f"     Entry: ${opp.get('entry_price', 0):.2f} | "
                          f"Stop: ${opp.get('stop_loss', 0):.2f} | "
                          f"Target: ${opp.get('target_price', 0):.2f}")
                    print(f"     R:R = 1:{opp.get('risk_reward', 0):.1f}")
                    print()
            
            if other_grade:
                print("📊 OTHER OPPORTUNITIES (C/D/F):")
                for opp in other_grade[:5]:  # Show only top 5 others
                    print(f"  📊 {opp.get('symbol', 'N/A')} - Grade {opp.get('grade', 'N/A')} "
                          f"({opp.get('confidence', 0)*100:.1f}%)")
                    print(f"     Entry: ${opp.get('entry_price', 0):.2f} | "
                          f"Stop: ${opp.get('stop_loss', 0):.2f} | "
                          f"Target: ${opp.get('target_price', 0):.2f}")
                    print()
        else:
            print("❌ No opportunities found in simple scanner")
            
    except Exception as e:
        print(f"❌ Simple scanner error: {e}")
    
    # Test the advanced scanner
    print("\n📊 Testing Advanced Scanner:")
    try:
        advanced_results = run_ttm_squeeze_scan()
        print("\n🎯 Advanced Scanner Results:")
        print(advanced_results)
        
    except Exception as e:
        print(f"❌ Advanced scanner error: {e}")

def test_alert_system():
    """Test the alert system for A/B grade opportunities."""
    print("\n🔔 Testing Alert System")
    print("=" * 30)
    
    try:
        # Scan for opportunities
        results = scan_ttm_squeeze_opportunities(min_grade='C', max_results=50)
        
        if 'top_opportunities' in results:
            opportunities = results['top_opportunities']
            
            # Filter for A/B grades
            high_grade_opportunities = [
                opp for opp in opportunities 
                if opp.get('grade', 'F') in ['A+', 'A', 'B']
            ]
            
            if high_grade_opportunities:
                print(f"🚨 ALERT: Found {len(high_grade_opportunities)} A/B grade opportunities!")
                
                for opp in high_grade_opportunities:
                    print(f"\n🎯 ALERT: {opp.get('symbol', 'N/A')} - Grade {opp.get('grade', 'N/A')}")
                    print(f"   Confidence: {opp.get('confidence', 0)*100:.1f}%")
                    print(f"   Entry: ${opp.get('entry_price', 0):.2f}")
                    print(f"   Stop Loss: ${opp.get('stop_loss', 0):.2f}")
                    print(f"   Target: ${opp.get('target_price', 0):.2f}")
                    print(f"   Risk:Reward = 1:{opp.get('risk_reward', 0):.1f}")
                    
                return True
            else:
                print("ℹ️ No A/B grade opportunities found at this time")
                return False
        else:
            print("❌ No scan results available")
            return False
            
    except Exception as e:
        print(f"❌ Alert system error: {e}")
        return False

def show_grading_criteria():
    """Show the grading criteria."""
    print("\n📋 TTM Squeeze Grading Criteria")
    print("=" * 35)
    print("Grade | Confidence | Description")
    print("-" * 35)
    print("A+    | 90-100%    | Perfect setup - all criteria met")
    print("A     | 85-89%     | Excellent setup - strong signals")
    print("B     | 75-84%     | Good setup - most criteria met")
    print("C     | 65-74%     | Average setup - some criteria met")
    print("D     | 55-64%     | Weak setup - few criteria met")
    print("F     | <55%       | Poor setup - avoid")
    print()
    print("🎯 Criteria Evaluated:")
    print("• Squeeze Release (Bollinger Bands vs Keltner Channels)")
    print("• Histogram Build (3+ consecutive rising bars)")
    print("• EMA Confirmation (8-EMA rising)")
    print("• Momentum Confirmation (Momentum(12) rising)")
    print("• Price Filter (Close > 5-EMA)")
    print("• SqueezeLine Threshold (>50%)")

if __name__ == "__main__":
    print("🚀 TTM Squeeze Grading System Test")
    print("=" * 40)
    
    # Show grading criteria
    show_grading_criteria()
    
    # Test grading system
    test_grading_system()
    
    # Test alert system
    print("\n" + "=" * 50)
    has_alerts = test_alert_system()
    
    if has_alerts:
        print("\n✅ Alert system working - A/B grade opportunities detected!")
    else:
        print("\n📊 Alert system working - no A/B grades found (normal)")
    
    print("\n🎯 Test Complete!")
    print("\nTo use the full GUI interface, run: python launch_gui.py")
