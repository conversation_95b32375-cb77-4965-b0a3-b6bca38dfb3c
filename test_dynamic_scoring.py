#!/usr/bin/env python3
"""
Test Dynamic Scoring System
"""

from ai_ttm_dynamic_screener import DynamicTTMScreener

def main():
    print("🔍 TESTING DYNAMIC PATTERN SCORING")
    print("=" * 50)

    screener = DynamicTTMScreener()
    if screener.model is None or screener.pattern_config is None:
        print("❌ Required files not loaded")
        return

    # Test with just a few symbols to see detailed scoring
    test_symbols = ['AAPL', 'MSFT', 'NVDA']
    print(f"Testing {len(test_symbols)} symbols with 30% threshold...")

    opportunities = screener.scan_symbols(test_symbols, min_composite_score=0.3)

    print(f"\nFound {len(opportunities)} opportunities:")
    for opp in opportunities:
        symbol = opp['symbol']
        ai_score = opp['ai_score']
        pattern_score = opp['pattern_score']
        composite_score = opp['composite_score']
        print(f"{symbol}: AI={ai_score:.1%}, Pattern={pattern_score:.1%}, Composite={composite_score:.1%}")

    if len(opportunities) == 0:
        print("\nNo opportunities found even at 30% threshold.")
        print("This suggests current market conditions don't align with historical winning patterns.")

if __name__ == "__main__":
    main()
