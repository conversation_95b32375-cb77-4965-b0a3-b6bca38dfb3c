#!/usr/bin/env python3
"""Simple test of Alpaca options API with the provided credentials."""

import requests
import json

def test_alpaca_options():
    """Test Alpaca options API with the exact credentials provided."""
    
    url = "https://data.sandbox.alpaca.markets/v1beta1/options/bars"
    
    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": "PK43FUDB28UZYZ87BT2V",
        "APCA-API-SECRET-KEY": "ICvalUW5EpIlQnInIidzIoDXrqQMdJ2CZJCD7RKg"
    }
    
    # Test 1: Basic API call without parameters
    print("🔍 Test 1: Basic Alpaca Options API call...")
    try:
        response = requests.get(url, headers=headers, timeout=15)
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS! Got JSON response")
            print(f"📊 Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            
            if isinstance(data, dict) and "bars" in data:
                bars = data["bars"]
                print(f"📊 Number of option symbols: {len(bars)}")
                
                if bars:
                    symbols = list(bars.keys())[:5]
                    print(f"📊 Sample symbols: {symbols}")
                    
                    # Show data for first symbol
                    if symbols:
                        first_symbol = symbols[0]
                        first_data = bars[first_symbol]
                        print(f"\n📊 Data for {first_symbol}:")
                        print(f"   Number of bars: {len(first_data)}")
                        if first_data:
                            print(f"   Latest bar: {first_data[-1]}")
                else:
                    print("📊 No bars data found")
            else:
                print(f"📊 Response structure: {data}")
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Error response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

    # Test 2: Try with AAPL filter
    print("\n🔍 Test 2: AAPL options filter...")
    params = {
        "symbols": "AAPL*",
        "limit": 50,
        "sort": "asc",
        "timeframe": "1Day"
    }
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=15)
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS! Got AAPL options")
            
            if isinstance(data, dict) and "bars" in data:
                bars = data["bars"]
                print(f"📊 Number of AAPL option symbols: {len(bars)}")
                
                if bars:
                    symbols = list(bars.keys())[:3]
                    print(f"📊 Sample AAPL symbols: {symbols}")
                    
                    # Test symbol parsing
                    if symbols:
                        test_symbol = symbols[0]
                        print(f"\n🧪 Testing symbol parser on: {test_symbol}")
                        
                        # Simple parsing test
                        import re
                        match = re.search(r'(\d{6}[CP])', test_symbol)
                        if match:
                            date_cp_start = match.start()
                            underlying = test_symbol[:date_cp_start]
                            date_cp = test_symbol[date_cp_start:date_cp_start+7]
                            strike_part = test_symbol[date_cp_start+7:date_cp_start+15]
                            
                            print(f"   Underlying: {underlying}")
                            print(f"   Date+Type: {date_cp}")
                            print(f"   Strike: {strike_part} -> ${float(strike_part)/1000:.2f}")
                        else:
                            print("   Could not parse symbol format")
                else:
                    print("📊 No AAPL options found")
            else:
                print(f"📊 Response structure: {data}")
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Error response: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    test_alpaca_options()
