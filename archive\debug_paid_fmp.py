#!/usr/bin/env python3
"""Debug paid FMP account to get actual options data working."""

import requests
import json
import time

def test_paid_fmp_options():
    """Test all FMP options endpoints with paid account."""
    print("🔍 DEBUGGING PAID FMP OPTIONS DATA")
    print("=" * 60)
    
    api_key = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
    symbol = "AAPL"
    
    # All possible FMP options endpoints for paid accounts
    endpoints = [
        # Standard options endpoints
        ("Options Chain V3", f"https://financialmodelingprep.com/api/v3/options-chain/{symbol}"),
        ("Options Chain V4", f"https://financialmodelingprep.com/api/v4/options-chain/{symbol}"),
        
        # Alternative options endpoints
        ("Options Contracts", f"https://financialmodelingprep.com/api/v3/options-contracts/{symbol}"),
        ("Available Options", f"https://financialmodelingprep.com/api/v3/available-options/{symbol}"),
        
        # Real-time options (paid feature)
        ("Real-time Options", f"https://financialmodelingprep.com/api/v3/options/real-time/{symbol}"),
        
        # Options with expiration dates
        ("Options by Date", f"https://financialmodelingprep.com/api/v3/options/{symbol}"),
        
        # Try with specific expiration date
        ("Options Specific Date", f"https://financialmodelingprep.com/api/v3/options/{symbol}/2025-01-17"),
        
        # Historical options
        ("Historical Options", f"https://financialmodelingprep.com/api/v3/historical-options/{symbol}"),
    ]
    
    for name, url in endpoints:
        print(f"\n📊 Testing: {name}")
        print(f"URL: {url}")
        print("-" * 40)
        
        try:
            params = {"apikey": api_key}
            response = requests.get(url, params=params, timeout=15)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    if isinstance(data, list):
                        print(f"✅ List with {len(data)} items")
                        if data:
                            first_item = data[0]
                            print(f"First item keys: {list(first_item.keys()) if isinstance(first_item, dict) else 'Not dict'}")
                            if isinstance(first_item, dict):
                                # Show sample option data
                                print(f"Sample option:")
                                for key, value in list(first_item.items())[:5]:
                                    print(f"  {key}: {value}")
                                print("  ...")
                        else:
                            print("❌ Empty list")
                    
                    elif isinstance(data, dict):
                        print(f"✅ Dict with keys: {list(data.keys())}")
                        if data:
                            # Check for nested options data
                            for key in ['options', 'optionChain', 'chain', 'contracts']:
                                if key in data:
                                    options_data = data[key]
                                    print(f"Found options in '{key}': {len(options_data) if isinstance(options_data, list) else 'Not list'}")
                                    if isinstance(options_data, list) and options_data:
                                        sample = options_data[0]
                                        print(f"Sample option keys: {list(sample.keys()) if isinstance(sample, dict) else 'Not dict'}")
                        else:
                            print("❌ Empty dict")
                    
                    else:
                        print(f"Unexpected type: {type(data)}")
                        print(f"Data: {str(data)[:100]}...")
                        
                except json.JSONDecodeError:
                    print(f"❌ Invalid JSON: {response.text[:200]}...")
            
            elif response.status_code == 403:
                print("❌ FORBIDDEN - Check API permissions")
            elif response.status_code == 429:
                print("❌ RATE LIMITED - Waiting...")
                time.sleep(2)
            else:
                print(f"❌ Error {response.status_code}: {response.text[:100]}...")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
        
        time.sleep(0.5)  # Rate limiting

def check_account_limits():
    """Check FMP account status and limits."""
    print(f"\n🔍 CHECKING PAID ACCOUNT STATUS")
    print("=" * 60)
    
    api_key = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
    
    # Check API usage and limits
    try:
        # Test basic endpoint first
        quote_url = f"https://financialmodelingprep.com/api/v3/quote/AAPL"
        params = {"apikey": api_key}
        response = requests.get(quote_url, params=params, timeout=10)
        
        print(f"Basic API Status: {response.status_code}")
        print(f"Rate Limit Headers:")
        for header, value in response.headers.items():
            if 'limit' in header.lower() or 'remaining' in header.lower():
                print(f"  {header}: {value}")
        
        if response.status_code == 200:
            data = response.json()
            if data:
                print(f"✅ Basic API working - Stock price: ${data[0].get('price', 'N/A')}")
            else:
                print("❌ Basic API returns empty data")
        else:
            print(f"❌ Basic API failed: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Account check failed: {e}")

def test_alternative_symbols():
    """Test options data for different symbols."""
    print(f"\n🔍 TESTING DIFFERENT SYMBOLS")
    print("=" * 60)
    
    api_key = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
    
    # Test multiple symbols - some might have better options data
    symbols = ["SPY", "QQQ", "TSLA", "MSFT", "NVDA"]
    
    for symbol in symbols:
        print(f"\n📊 Testing {symbol}...")
        
        # Try the most likely working endpoint
        url = f"https://financialmodelingprep.com/api/v3/options-chain/{symbol}"
        
        try:
            params = {"apikey": api_key}
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list) and data:
                    print(f"  ✅ {symbol}: {len(data)} options found!")
                    # Show sample
                    sample = data[0]
                    if isinstance(sample, dict):
                        print(f"  Sample keys: {list(sample.keys())}")
                        strike = sample.get('strike', sample.get('strikePrice', 'N/A'))
                        price = sample.get('lastPrice', sample.get('price', 'N/A'))
                        print(f"  Strike: {strike}, Price: {price}")
                else:
                    print(f"  ❌ {symbol}: Empty response")
            else:
                print(f"  ❌ {symbol}: Error {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ {symbol}: Exception {e}")
        
        time.sleep(0.3)

if __name__ == "__main__":
    check_account_limits()
    test_paid_fmp_options()
    test_alternative_symbols()
    
    print(f"\n🎯 PAID ACCOUNT DIAGNOSIS COMPLETE!")
    print("=" * 60)
    print("Next steps based on results:")
    print("1. If any endpoint returns data - we'll use that one")
    print("2. If all empty - contact FMP support about options access")
    print("3. If rate limited - we'll add more delays")
    print("4. If forbidden - check API key permissions in FMP dashboard")
