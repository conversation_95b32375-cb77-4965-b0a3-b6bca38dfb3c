#!/usr/bin/env python3
"""Test script for Alpaca options API integration."""

from options_strategies import OptionsStrategies
from logger_util import info, warning

def test_alpaca_options():
    """Test the Alpaca options API integration."""
    print("🧪 Testing Alpaca Options API Integration")
    print("=" * 50)
    
    # Initialize options strategies
    options = OptionsStrategies()
    
    # Test symbols
    test_symbols = ["AAPL", "TSLA", "SPY"]
    
    for symbol in test_symbols:
        print(f"\n📊 Testing {symbol}...")
        
        # Test stock price
        print(f"1. Getting stock price for {symbol}...")
        stock_price = options._get_stock_price(symbol)
        print(f"   Stock Price: ${stock_price:.2f}")
        
        # Test options chain
        print(f"2. Getting options chain for {symbol}...")
        options_chain = options._get_options_chain(symbol)
        print(f"   Options Contracts Found: {len(options_chain)}")
        
        if options_chain:
            # Show sample contracts
            print(f"   Sample contracts:")
            for i, contract in enumerate(options_chain[:3]):  # Show first 3
                print(f"     {i+1}. {contract['symbol']} - Strike: ${contract['strike']:.2f}, Type: {contract['type']}, Exp: {contract['expiration']}")
            
            # Test options quotes
            print(f"3. Getting quotes for first few contracts...")
            sample_symbols = [contract['symbol'] for contract in options_chain[:5]]
            quotes = options._get_options_quotes(sample_symbols)
            print(f"   Quotes Retrieved: {len(quotes)}")
            
            if quotes:
                for symbol, quote in list(quotes.items())[:2]:  # Show first 2
                    print(f"     {symbol}: Bid=${quote['bid']:.2f}, Ask=${quote['ask']:.2f}")
        
        print("-" * 30)
    
    print(f"\n✅ Test completed!")

if __name__ == "__main__":
    test_alpaca_options()
