#!/usr/bin/env python3
"""System Verification Script

Quick verification that all components are working correctly.
"""
import sys
import os

# Add all necessary directories to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'core'))
sys.path.insert(0, os.path.join(current_dir, 'gui'))
sys.path.insert(0, os.path.join(current_dir, 'scanners'))
sys.path.insert(0, os.path.join(current_dir, 'trading'))
sys.path.insert(0, os.path.join(current_dir, 'utils'))

def test_core_imports():
    """Test core system imports."""
    print("Testing Core System...")
    
    try:
        from config import get_api_key, public_keys_summary
        print("✅ Config system: OK")
        
        from logger_util import info, warning
        print("✅ Logger system: OK")
        
        return True
    except Exception as e:
        print(f"❌ Core system error: {e}")
        return False

def test_scanner_imports():
    """Test scanner imports."""
    print("\nTesting Scanner System...")
    
    try:
        from proper_ttm_squeeze_scanner import TTMSqueezeScanner
        print("✅ TTM Scanner: OK")
        
        from ttm_squeeze_watchlist import TTMSqueezeWatchlist
        print("✅ TTM Watchlist: OK")
        
        return True
    except Exception as e:
        print(f"❌ Scanner system error: {e}")
        return False

def test_trading_imports():
    """Test trading system imports."""
    print("\nTesting Trading System...")
    
    try:
        from alpaca_trading import AlpacaTrader
        print("✅ Alpaca Trading: OK")
        
        from paper_trading_system import PaperTradingSystem
        print("✅ Paper Trading: OK")
        
        return True
    except Exception as e:
        print(f"❌ Trading system error: {e}")
        return False

def test_gui_imports():
    """Test GUI imports."""
    print("\nTesting GUI System...")
    
    try:
        # Don't actually import the GUI (would launch it)
        # Just check if the file exists and is importable
        import importlib.util
        
        gui_path = os.path.join(current_dir, 'gui', 'tkinter_trading_interface.py')
        if os.path.exists(gui_path):
            print("✅ GUI Interface: File exists")
            return True
        else:
            print("❌ GUI Interface: File missing")
            return False
            
    except Exception as e:
        print(f"❌ GUI system error: {e}")
        return False

def test_api_keys():
    """Test API key configuration."""
    print("\nTesting API Configuration...")
    
    try:
        from config import public_keys_summary
        
        keys = public_keys_summary()
        
        required_keys = ['FMP_API_KEY', 'ALPACA_API_KEY', 'ALPACA_API_SECRET']
        all_configured = True
        
        for key in required_keys:
            if key in keys and keys[key] and keys[key] != "****":
                print(f"✅ {key}: Configured")
            else:
                print(f"❌ {key}: Not configured")
                all_configured = False
        
        return all_configured
        
    except Exception as e:
        print(f"❌ API key test error: {e}")
        return False

def main():
    """Run all verification tests."""
    print("🔍 TTM Trading System - Verification")
    print("=" * 50)
    
    tests = [
        ("Core System", test_core_imports),
        ("Scanner System", test_scanner_imports),
        ("Trading System", test_trading_imports),
        ("GUI System", test_gui_imports),
        ("API Configuration", test_api_keys),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}: Exception - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Verification Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL SYSTEMS OPERATIONAL!")
        print("\n🚀 System is ready to use:")
        print("   python main.py")
        print("\n📋 Available features:")
        print("   • TTM Squeeze Scanner")
        print("   • Alpaca Paper Trading")
        print("   • Alpaca Live Trading")
        print("   • AI Market Chat")
        print("   • Options Analysis")
    else:
        print("⚠️  Some systems need attention.")
        print("\n💡 Common fixes:")
        print("   • Install dependencies: pip install -r config/requirements.txt")
        print("   • Configure API keys in config/config.env")
        print("   • Check internet connection")

if __name__ == "__main__":
    main()
