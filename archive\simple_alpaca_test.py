import requests

# Your API credentials
api_key = "PK43FUDB28UZYZ87BT2V"
api_secret = "ICvalUW5EpIlQnInIidzIoDXrqQMdJ2CZJCD7RKg"

headers = {
    "accept": "application/json",
    "APCA-API-KEY-ID": api_key,
    "APCA-API-SECRET-KEY": api_secret
}

# Test stock quote first
print("Testing stock quote...")
url = "https://data.sandbox.alpaca.markets/v2/stocks/AAPL/quotes/latest"
response = requests.get(url, headers=headers)
print(f"Status: {response.status_code}")
print(f"Response: {response.text}")

print("\n" + "="*50)

# Test options snapshots
print("Testing options snapshots...")
url = "https://data.sandbox.alpaca.markets/v1beta1/options/snapshots"
params = {"symbols": "AAPL", "feed": "sip"}
response = requests.get(url, headers=headers, params=params)
print(f"Status: {response.status_code}")
print(f"Response: {response.text}")
