#!/usr/bin/env python3
"""Debug FMP API to see exactly what data we're getting."""

import requests
import json

def debug_fmp_api():
    """Debug FMP API responses to understand the data format."""
    print("🔍 DEBUGGING FMP API RESPONSES")
    print("=" * 60)
    
    api_key = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
    symbol = "AAPL"
    
    # Test all FMP endpoints and show raw responses
    endpoints = [
        ("Options Chain V3", f"https://financialmodelingprep.com/api/v3/options-chain/{symbol}"),
        ("Options Chain V4", f"https://financialmodelingprep.com/api/v4/options-chain/{symbol}"),
        ("Options V3", f"https://financialmodelingprep.com/api/v3/options/{symbol}"),
        ("Available Options", f"https://financialmodelingprep.com/api/v3/available-options/{symbol}"),
        ("Options Contracts", f"https://financialmodelingprep.com/api/v3/options-contracts/{symbol}"),
        ("Real-time Options", f"https://financialmodelingprep.com/api/v3/options/real-time/{symbol}")
    ]
    
    for name, url in endpoints:
        print(f"\n📊 Testing: {name}")
        print(f"URL: {url}")
        print("-" * 50)
        
        try:
            params = {"apikey": api_key}
            response = requests.get(url, params=params, timeout=15)
            
            print(f"Status Code: {response.status_code}")
            print(f"Content-Type: {response.headers.get('content-type', 'N/A')}")
            print(f"Content-Length: {response.headers.get('content-length', 'N/A')}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"Response Type: {type(data)}")
                    
                    if isinstance(data, list):
                        print(f"List Length: {len(data)}")
                        if data:
                            print(f"First Item Keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'Not a dict'}")
                            print(f"Sample Item: {json.dumps(data[0], indent=2)[:300]}...")
                        else:
                            print("❌ EMPTY LIST - This is why we're not getting data!")
                    
                    elif isinstance(data, dict):
                        print(f"Dict Keys: {list(data.keys())}")
                        if data:
                            print(f"Sample Data: {json.dumps(data, indent=2)[:500]}...")
                        else:
                            print("❌ EMPTY DICT - This is why we're not getting data!")
                    
                    else:
                        print(f"Unexpected Type: {type(data)}")
                        print(f"Raw Data: {str(data)[:200]}...")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON Decode Error: {e}")
                    print(f"Raw Response: {response.text[:300]}...")
            
            elif response.status_code == 403:
                print("❌ FORBIDDEN - API key might not have options access")
                print(f"Response: {response.text[:200]}")
            
            elif response.status_code == 429:
                print("❌ RATE LIMITED")
                print(f"Response: {response.text[:200]}")
            
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text[:300]}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def test_fmp_account_info():
    """Check FMP account limits and permissions."""
    print(f"\n🔍 CHECKING FMP ACCOUNT INFO")
    print("=" * 60)
    
    api_key = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
    
    # Check account status
    account_url = f"https://financialmodelingprep.com/api/v3/profile"
    
    try:
        params = {"apikey": api_key}
        response = requests.get(account_url, params=params, timeout=10)
        
        print(f"Account Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Account Info: {json.dumps(data, indent=2)}")
        else:
            print(f"Account Error: {response.text[:200]}")
            
    except Exception as e:
        print(f"Account Check Failed: {e}")

def test_working_endpoints():
    """Test endpoints we know should work."""
    print(f"\n🔍 TESTING WORKING ENDPOINTS")
    print("=" * 60)
    
    api_key = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
    symbol = "AAPL"
    
    working_endpoints = [
        ("Stock Quote", f"https://financialmodelingprep.com/api/v3/quote/{symbol}"),
        ("Company Profile", f"https://financialmodelingprep.com/api/v3/profile/{symbol}"),
        ("Historical Prices", f"https://financialmodelingprep.com/api/v3/historical-price-full/{symbol}?limit=5")
    ]
    
    for name, url in working_endpoints:
        print(f"\n📊 Testing: {name}")
        
        try:
            params = {"apikey": api_key}
            response = requests.get(url, params=params, timeout=10)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list) and data:
                    print(f"✅ SUCCESS: Got {len(data)} items")
                    print(f"Sample: {list(data[0].keys()) if isinstance(data[0], dict) else data[0]}")
                elif isinstance(data, dict) and data:
                    print(f"✅ SUCCESS: Got dict with keys: {list(data.keys())}")
                else:
                    print(f"⚠️ Empty response")
            else:
                print(f"❌ Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

if __name__ == "__main__":
    debug_fmp_api()
    test_fmp_account_info()
    test_working_endpoints()
    
    print(f"\n🎯 DIAGNOSIS COMPLETE!")
    print("=" * 60)
    print("Check the output above to see:")
    print("1. Which endpoints return empty data vs real data")
    print("2. If your API key has options access")
    print("3. What the actual data format looks like")
    print("4. Any rate limiting or permission issues")
