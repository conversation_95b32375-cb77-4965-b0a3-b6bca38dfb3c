#!/usr/bin/env python3
"""
Final Alpaca API check with specific troubleshooting steps.
"""

import requests
import json

def test_alpaca_final():
    """Final comprehensive test with specific troubleshooting."""

    # Use the new API credentials
    api_key = "PKTD043BAZB9PBVJ5OI7"
    secret_key = "8C9szBhULNFtytP2d5NhlSgjHjdVKaUtZxryOZqZ"

    print("🔧 Alpaca API Final Troubleshooting")
    print("=" * 60)
    print(f"API Key: {api_key}")
    print(f"Secret: {secret_key[:8]}...{secret_key[-8:]}")

    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": api_key,
        "APCA-API-SECRET-KEY": secret_key
    }

    # Test the most basic endpoint first
    print(f"\n🧪 Testing Basic Paper Account Access...")
    try:
        response = requests.get(
            "https://paper-api.alpaca.markets/v2/account",
            headers=headers,
            timeout=10
        )

        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")

        if response.status_code == 200:
            print("✅ SUCCESS! API credentials are working.")
            data = response.json()
            print(f"Account ID: {data.get('id')}")
            print(f"Account Status: {data.get('status')}")
            return True

        elif response.status_code == 403:
            print("❌ 403 FORBIDDEN - API key issues detected")

        elif response.status_code == 401:
            print("❌ 401 UNAUTHORIZED - Authentication failed")

    except Exception as e:
        print(f"❌ Request failed: {e}")

    # Provide specific troubleshooting steps
    print(f"\n🔍 SPECIFIC TROUBLESHOOTING STEPS:")
    print("=" * 60)

    print("1. 🌐 VERIFY API KEY IN ALPACA DASHBOARD:")
    print("   • Go to: https://app.alpaca.markets/paper/dashboard")
    print("   • Navigate to: Account → API Keys")
    print("   • Check if this key exists and is active:")
    print(f"     Key ID: {api_key}")
    print("   • If not found, generate a new API key pair")

    print(f"\n2. 📋 CHECK ACCOUNT STATUS:")
    print("   • Verify your account is approved and active")
    print("   • Check if any verification steps are pending")
    print("   • Ensure account is not suspended")

    print(f"\n3. 🔑 API KEY PERMISSIONS:")
    print("   • Ensure the API key has trading permissions")
    print("   • Check if the key is restricted to specific IPs")
    print("   • Verify the key hasn't expired")

    print(f"\n4. 🎯 ACCOUNT TYPE VERIFICATION:")
    print("   • Confirm you're using Paper Trading credentials")
    print("   • Paper and Live accounts have separate API keys")
    print("   • Don't mix paper and live credentials")

    print(f"\n5. 📞 CONTACT ALPACA SUPPORT:")
    print("   • If all above steps check out, contact Alpaca support")
    print("   • Provide them with this API key ID for investigation")
    print("   • Support: https://alpaca.markets/support")

    print(f"\n6. 🔄 ALTERNATIVE: CREATE NEW API KEY:")
    print("   • Generate a fresh API key pair in the dashboard")
    print("   • Update your config.env file with new credentials")
    print("   • Test again with the new key")

    # Test with a simple curl command equivalent
    print(f"\n🧪 MANUAL TEST COMMAND:")
    print("You can test this manually with curl:")
    print(f'curl -H "APCA-API-KEY-ID: {api_key}" \\')
    print(f'     -H "APCA-API-SECRET-KEY: {secret_key}" \\')
    print(f'     https://paper-api.alpaca.markets/v2/account')

    return False

if __name__ == "__main__":
    success = test_alpaca_final()

    if not success:
        print(f"\n⚠️  IMMEDIATE ACTION REQUIRED:")
        print("1. Log into your Alpaca dashboard")
        print("2. Verify/regenerate your API keys")
        print("3. Update config.env with working credentials")
        print("4. Contact Alpaca support if issues persist")
    else:
        print(f"\n✅ API is working! You can proceed with options trading setup.")
