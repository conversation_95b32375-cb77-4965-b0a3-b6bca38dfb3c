#!/usr/bin/env python3
"""Test Alpaca authentication with both paper and live endpoints."""

import requests
import json

def test_alpaca_authentication():
    """Test Alpaca authentication with the provided credentials."""
    print("🔍 Testing Alpaca Authentication")
    print("=" * 50)
    
    # Credentials provided
    api_key_id = "PK43FUDB28UZYZ87BT2V"
    api_secret_key = "ICvalUW5EpIlQnInIidzIoDXrqQMdJ2CZJCD7RKg"
    
    # Test both paper and live endpoints
    endpoints = [
        ("Paper Trading", "https://paper-api.alpaca.markets"),
        ("Live Trading", "https://api.alpaca.markets")
    ]
    
    headers = {
        "APCA-API-KEY-ID": api_key_id,
        "APCA-API-SECRET-KEY": api_secret_key
    }
    
    for name, base_url in endpoints:
        print(f"\n📊 Testing {name}")
        print(f"URL: {base_url}")
        print("-" * 30)
        
        # Test account endpoint
        account_url = f"{base_url}/v2/account"
        
        try:
            response = requests.get(account_url, headers=headers, timeout=10)
            print(f"Account Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ SUCCESS! Account authenticated")
                print(f"Account ID: {data.get('id', 'N/A')}")
                print(f"Status: {data.get('status', 'N/A')}")
                print(f"Equity: ${data.get('equity', 'N/A')}")
                print(f"Buying Power: ${data.get('buying_power', 'N/A')}")
                
                # Test options contracts endpoint
                print(f"\n🔍 Testing Options Contracts...")
                options_url = f"{base_url}/v2/options/contracts"
                params = {"underlying_symbols": "AAPL", "limit": 5}
                
                options_response = requests.get(options_url, headers=headers, params=params, timeout=10)
                print(f"Options Status: {options_response.status_code}")
                
                if options_response.status_code == 200:
                    options_data = options_response.json()
                    if "option_contracts" in options_data:
                        contracts = options_data["option_contracts"]
                        print(f"✅ Found {len(contracts)} option contracts")
                        if contracts:
                            contract = contracts[0]
                            print(f"Sample: {contract['symbol']} - Strike: ${contract['strike_price']}")
                    else:
                        print("⚠️ No option_contracts in response")
                else:
                    print(f"❌ Options failed: {options_response.status_code}")
                    print(f"Error: {options_response.text[:200]}")
                
            elif response.status_code == 403:
                print(f"❌ FORBIDDEN: Credentials not authorized for {name}")
                print(f"Response: {response.text[:200]}")
            elif response.status_code == 401:
                print(f"❌ UNAUTHORIZED: Invalid credentials")
                print(f"Response: {response.text[:200]}")
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def test_market_data_api():
    """Test Alpaca Market Data API (should work with same credentials)."""
    print(f"\n🔍 Testing Alpaca Market Data API")
    print("-" * 50)
    
    api_key_id = "PK43FUDB28UZYZ87BT2V"
    api_secret_key = "ICvalUW5EpIlQnInIidzIoDXrqQMdJ2CZJCD7RKg"
    
    headers = {
        "APCA-API-KEY-ID": api_key_id,
        "APCA-API-SECRET-KEY": api_secret_key
    }
    
    # Test market data endpoints
    market_data_endpoints = [
        ("Stock Quote", "https://data.alpaca.markets/v2/stocks/AAPL/quotes/latest"),
        ("Options Snapshots", "https://data.sandbox.alpaca.markets/v1beta1/options/snapshots/AAPL")
    ]
    
    for name, url in market_data_endpoints:
        print(f"\n📊 Testing {name}")
        print(f"URL: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ SUCCESS! Got market data")
                print(f"Response keys: {list(data.keys())}")
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def test_credentials_format():
    """Test if credentials are in the right format."""
    print(f"\n🔍 Testing Credentials Format")
    print("-" * 50)
    
    api_key_id = "PK43FUDB28UZYZ87BT2V"
    api_secret_key = "ICvalUW5EpIlQnInIidzIoDXrqQMdJ2CZJCD7RKg"
    
    print(f"API Key ID: {api_key_id}")
    print(f"Length: {len(api_key_id)}")
    print(f"Starts with PK: {api_key_id.startswith('PK')}")
    
    print(f"\nAPI Secret Key: {api_secret_key}")
    print(f"Length: {len(api_secret_key)}")
    print(f"Format looks correct: {len(api_secret_key) > 30}")

if __name__ == "__main__":
    test_credentials_format()
    test_alpaca_authentication()
    test_market_data_api()
    
    print(f"\n🎉 Alpaca Authentication Tests Completed!")
    print("=" * 60)
