"""Live TTM Squeeze Trading System

This system will:
1. Find the most promising TTM squeeze setups
2. Execute actual trades through a broker API
3. Implement proper trailing stops
4. Monitor positions in real-time
5. Manage risk automatically

NOTE: This is a framework for live trading. You'll need to:
- Connect to your broker's API (Alpaca, Interactive Brokers, etc.)
- Add your API credentials
- Test with paper trading first
"""
from __future__ import annotations

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import requests
import json

from config import get_api_key
from logger_util import info, warning
from ttm_options_specialist import get_ttm_options_specialist
from ttm_squeeze_watchlist import TTMSqueezeWatchlist


class LiveTTMTradingSystem:
    """Live trading system for TTM Squeeze setups with trailing stops."""
    
    def __init__(self):
        self.api_key = get_api_key('FMP_API_KEY')
        self.ttm_specialist = get_ttm_options_specialist()
        self.watchlist = TTMSqueezeWatchlist()
        
        # Trading parameters
        self.max_position_size = 1000  # Maximum $ per trade
        self.risk_per_trade = 0.02  # 2% risk per trade
        self.trailing_stop_pct = 0.03  # 3% trailing stop
        
        # Active positions
        self.active_positions = {}
        self.monitoring = False
        
        # Paper trading mode (set to False for live trading)
        self.paper_trading = True
        
        info("🚀 Live TTM Trading System initialized")
    
    def find_best_ttm_setup(self) -> Optional[Dict]:
        """Find the most promising TTM squeeze setup for trading."""
        info("🔍 Scanning for best TTM squeeze setup...")
        
        try:
            # Update watchlist to get latest data
            self.watchlist.update_watchlist()

            # Get top breakout candidates
            candidates = self.watchlist.get_top_breakout_candidates(20)
            
            if not candidates:
                warning("No TTM squeeze candidates found")
                return None
            
            # Score and rank candidates
            best_setup = None
            best_score = 0
            
            for candidate in candidates:
                score = self._score_ttm_setup(candidate)
                
                if score > best_score:
                    best_score = score
                    best_setup = candidate
            
            if best_setup and best_score >= 75:  # Minimum score threshold
                info(f"🎯 Best TTM setup found: {best_setup['symbol']} (Score: {best_score})")
                return best_setup
            else:
                info("No high-quality TTM setups meet trading criteria")
                return None
                
        except Exception as e:
            warning(f"Error finding TTM setup: {e}")
            return None
    
    def _score_ttm_setup(self, setup: Dict) -> int:
        """Score a TTM setup based on multiple factors."""
        score = 0
        
        # Base breakout score
        score += setup.get('breakout_score', 0)
        
        # Volume factor
        if setup.get('volume_ratio', 0) > 1.5:
            score += 10
        elif setup.get('volume_ratio', 0) > 1.2:
            score += 5
        
        # Squeeze duration (longer squeeze = higher score)
        duration = setup.get('squeeze_duration', 0)
        if duration > 15:
            score += 15
        elif duration > 10:
            score += 10
        elif duration > 5:
            score += 5
        
        # Price momentum
        if setup.get('momentum_direction') == 'BULLISH':
            score += 10
        
        # Timeframe preference (15min is optimal)
        if setup.get('timeframe') == '15min':
            score += 5
        
        return min(score, 100)  # Cap at 100
    
    def execute_ttm_trade(self, setup: Dict) -> bool:
        """Execute a trade based on TTM squeeze setup."""
        symbol = setup['symbol']
        current_price = setup['price']
        
        info(f"🎯 Executing TTM trade for {symbol} at ${current_price:.2f}")
        
        try:
            # Calculate position size based on risk
            position_size = self._calculate_position_size(current_price)
            
            # Calculate stop loss (3% below entry for bullish setup)
            stop_loss = current_price * (1 - self.trailing_stop_pct)
            
            # Calculate take profit (2:1 risk/reward ratio)
            take_profit = current_price + (2 * (current_price - stop_loss))
            
            # Create trade order
            trade_order = {
                'symbol': symbol,
                'side': 'buy',  # Assuming bullish breakout
                'quantity': int(position_size / current_price),
                'order_type': 'market',
                'entry_price': current_price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'trailing_stop_pct': self.trailing_stop_pct,
                'timestamp': datetime.now(),
                'setup_data': setup
            }
            
            # Execute the trade
            if self.paper_trading:
                success = self._execute_paper_trade(trade_order)
            else:
                success = self._execute_live_trade(trade_order)
            
            if success:
                # Add to active positions for monitoring
                self.active_positions[symbol] = trade_order
                info(f"✅ Trade executed: {symbol} - {trade_order['quantity']} shares at ${current_price:.2f}")
                info(f"   Stop Loss: ${stop_loss:.2f}, Take Profit: ${take_profit:.2f}")
                return True
            else:
                warning(f"❌ Trade execution failed for {symbol}")
                return False
                
        except Exception as e:
            warning(f"Error executing trade for {symbol}: {e}")
            return False
    
    def _calculate_position_size(self, price: float) -> float:
        """Calculate position size based on risk management."""
        # Risk 2% of account per trade
        account_value = 10000  # Assume $10k account (adjust as needed)
        risk_amount = account_value * self.risk_per_trade  # $200 risk

        # Position size based on stop loss distance
        stop_distance = price * self.trailing_stop_pct  # 3% stop
        position_size = risk_amount / stop_distance

        # Ensure minimum viable position size
        min_position = 100  # Minimum $100 position
        position_size = max(position_size, min_position)

        # Cap at maximum position size
        return min(position_size, self.max_position_size)
    
    def _execute_paper_trade(self, order: Dict) -> bool:
        """Execute paper trade (simulation)."""
        info(f"📝 PAPER TRADE: {order['side'].upper()} {order['quantity']} shares of {order['symbol']}")
        info(f"   Entry: ${order['entry_price']:.2f}")
        info(f"   Stop: ${order['stop_loss']:.2f}")
        info(f"   Target: ${order['take_profit']:.2f}")
        
        # Simulate successful execution
        return True
    
    def _execute_live_trade(self, order: Dict) -> bool:
        """Execute live trade through broker API."""
        # TODO: Implement actual broker API integration
        # Examples: Alpaca, Interactive Brokers, TD Ameritrade, etc.
        
        warning("🚨 LIVE TRADING NOT IMPLEMENTED - ADD BROKER API")
        warning("   To enable live trading:")
        warning("   1. Choose broker (Alpaca, IBKR, etc.)")
        warning("   2. Add API credentials")
        warning("   3. Implement broker-specific order execution")
        warning("   4. Test thoroughly with paper trading first")
        
        return False
    
    def monitor_positions(self):
        """Monitor active positions and manage trailing stops."""
        self.monitoring = True
        info("👁️ Starting position monitoring with trailing stops...")
        
        while self.monitoring:
            try:
                for symbol, position in list(self.active_positions.items()):
                    self._update_trailing_stop(symbol, position)
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                warning(f"Error monitoring positions: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _update_trailing_stop(self, symbol: str, position: Dict):
        """Update trailing stop for a position."""
        try:
            # Get current price
            current_price = self._get_current_price(symbol)
            if not current_price:
                return
            
            entry_price = position['entry_price']
            current_stop = position['stop_loss']
            trailing_pct = position['trailing_stop_pct']
            
            # Calculate new trailing stop
            if position['side'] == 'buy':  # Long position
                new_stop = current_price * (1 - trailing_pct)
                
                # Only move stop up, never down
                if new_stop > current_stop:
                    position['stop_loss'] = new_stop
                    info(f"📈 Trailing stop updated for {symbol}: ${new_stop:.2f}")
                    
                    # Update stop order with broker
                    if not self.paper_trading:
                        self._update_stop_order(symbol, new_stop)
                
                # Check if stop hit
                if current_price <= current_stop:
                    info(f"🛑 Stop loss hit for {symbol} at ${current_price:.2f}")
                    self._close_position(symbol, "Stop Loss")
                
                # Check if take profit hit
                elif current_price >= position['take_profit']:
                    info(f"🎯 Take profit hit for {symbol} at ${current_price:.2f}")
                    self._close_position(symbol, "Take Profit")
            
        except Exception as e:
            warning(f"Error updating trailing stop for {symbol}: {e}")
    
    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol."""
        try:
            url = f"https://financialmodelingprep.com/api/v3/quote/{symbol}?apikey={self.api_key}"
            response = requests.get(url, timeout=10)
            data = response.json()
            
            if data and len(data) > 0:
                return float(data[0]['price'])
            else:
                return None
                
        except Exception as e:
            warning(f"Error getting price for {symbol}: {e}")
            return None
    
    def _update_stop_order(self, symbol: str, new_stop: float):
        """Update stop order with broker."""
        # TODO: Implement broker-specific stop order update
        info(f"📝 PAPER: Updated stop order for {symbol} to ${new_stop:.2f}")
    
    def _close_position(self, symbol: str, reason: str):
        """Close a position."""
        if symbol in self.active_positions:
            position = self.active_positions[symbol]
            current_price = self._get_current_price(symbol)
            
            if current_price:
                entry_price = position['entry_price']
                pnl = (current_price - entry_price) * position['quantity']
                pnl_pct = ((current_price - entry_price) / entry_price) * 100
                
                info(f"🔚 Position closed: {symbol}")
                info(f"   Reason: {reason}")
                info(f"   Entry: ${entry_price:.2f}, Exit: ${current_price:.2f}")
                info(f"   P&L: ${pnl:.2f} ({pnl_pct:+.2f}%)")
            
            # Remove from active positions
            del self.active_positions[symbol]
    
    def start_automated_trading(self):
        """Start automated TTM trading system."""
        info("🚀 Starting automated TTM trading system...")
        
        # Start position monitoring in background
        monitor_thread = threading.Thread(target=self.monitor_positions, daemon=True)
        monitor_thread.start()
        
        # Main trading loop
        while True:
            try:
                # Find best TTM setup
                best_setup = self.find_best_ttm_setup()
                
                if best_setup:
                    symbol = best_setup['symbol']
                    
                    # Don't trade if already have position
                    if symbol not in self.active_positions:
                        success = self.execute_ttm_trade(best_setup)
                        
                        if success:
                            info(f"✅ New TTM trade executed: {symbol}")
                        else:
                            warning(f"❌ Failed to execute TTM trade: {symbol}")
                    else:
                        info(f"⏭️ Skipping {symbol} - already have position")
                else:
                    info("⏳ No qualifying TTM setups found, waiting...")
                
                # Wait before next scan (adjust frequency as needed)
                time.sleep(300)  # 5 minutes between scans
                
            except KeyboardInterrupt:
                info("🛑 Trading system stopped by user")
                break
            except Exception as e:
                warning(f"Error in trading loop: {e}")
                time.sleep(60)  # Wait on error
        
        # Stop monitoring
        self.monitoring = False
        info("🔚 Automated trading system stopped")
    
    def get_trading_status(self) -> Dict:
        """Get current trading system status."""
        return {
            'active_positions': len(self.active_positions),
            'positions': list(self.active_positions.keys()),
            'paper_trading': self.paper_trading,
            'monitoring': self.monitoring,
            'last_scan': datetime.now().strftime('%H:%M:%S')
        }


def run_live_ttm_trading():
    """Run the live TTM trading system."""
    print("🚀 LIVE TTM SQUEEZE TRADING SYSTEM")
    print("=" * 50)
    
    # Create trading system
    trading_system = LiveTTMTradingSystem()
    
    print(f"📊 Trading Parameters:")
    print(f"   Max Position Size: ${trading_system.max_position_size}")
    print(f"   Risk Per Trade: {trading_system.risk_per_trade * 100}%")
    print(f"   Trailing Stop: {trading_system.trailing_stop_pct * 100}%")
    print(f"   Paper Trading: {trading_system.paper_trading}")
    
    print(f"\n🎯 System will:")
    print(f"   1. Scan for best TTM squeeze setups")
    print(f"   2. Execute trades automatically")
    print(f"   3. Manage trailing stops")
    print(f"   4. Monitor positions in real-time")
    
    # Find and execute one trade as demo
    print(f"\n🔍 Finding best TTM setup...")
    best_setup = trading_system.find_best_ttm_setup()
    
    if best_setup:
        print(f"✅ Best setup found: {best_setup['symbol']}")
        print(f"   Score: {trading_system._score_ttm_setup(best_setup)}")
        print(f"   Price: ${best_setup['price']:.2f}")
        print(f"   Breakout Score: {best_setup.get('breakout_score', 'N/A')}")
        
        # Execute the trade
        success = trading_system.execute_ttm_trade(best_setup)
        
        if success:
            print(f"✅ Trade executed successfully!")
            print(f"📊 Active positions: {len(trading_system.active_positions)}")
        else:
            print(f"❌ Trade execution failed")
    else:
        print(f"⏳ No qualifying TTM setups found at this time")
    
    return trading_system


if __name__ == "__main__":
    # Run demo
    trading_system = run_live_ttm_trading()
    
    print(f"\n🚀 To start automated trading:")
    print(f"   trading_system.start_automated_trading()")
    print(f"\n⚠️ IMPORTANT:")
    print(f"   • Currently in PAPER TRADING mode")
    print(f"   • Add broker API for live trading")
    print(f"   • Test thoroughly before going live")
    print(f"   • Start with small position sizes")
