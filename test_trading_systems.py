#!/usr/bin/env python3
"""
Test Trading Systems Diagnostic
Check auto trading and execution functionality
"""

import sys
import os
sys.path.append('.')
sys.path.append('core')
sys.path.append('trading')

def test_api_keys():
    """Test if API keys are properly configured."""
    print("🔑 TESTING API KEYS")
    print("=" * 50)
    
    try:
        from core.config import get_api_key
        
        keys_to_test = [
            "ALPACA_API_KEY",
            "ALPACA_API_SECRET", 
            "FMP_API_KEY",
            "OPENAI_API_KEY"
        ]
        
        for key in keys_to_test:
            try:
                value = get_api_key(key)
                if value:
                    print(f"✅ {key}: Configured (length: {len(value)})")
                else:
                    print(f"❌ {key}: Empty or None")
            except Exception as e:
                print(f"❌ {key}: Error - {e}")
                
    except Exception as e:
        print(f"❌ Config module error: {e}")

def test_alpaca_connection():
    """Test Alpaca API connection."""
    print("\n🔌 TESTING ALPACA CONNECTION")
    print("=" * 50)
    
    try:
        from trading.alpaca_trading import AlpacaTrader
        
        # Test paper trading connection
        print("Testing paper trading connection...")
        trader = AlpacaTrader(paper_trading=True)
        
        # Test account info
        account = trader.get_account_info()
        if account:
            print(f"✅ Paper account connected")
            print(f"   Buying Power: ${account.get('buying_power', 'N/A')}")
            print(f"   Cash: ${account.get('cash', 'N/A')}")
        else:
            print("❌ Failed to get account info")
            
        # Test getting a price
        print("\nTesting price data...")
        price = trader.get_current_price("AAPL")
        if price:
            print(f"✅ Price data working: AAPL = ${price}")
        else:
            print("❌ Failed to get price data")
            
    except Exception as e:
        print(f"❌ Alpaca connection error: {e}")
        import traceback
        traceback.print_exc()

def test_order_execution():
    """Test order execution system."""
    print("\n📋 TESTING ORDER EXECUTION")
    print("=" * 50)
    
    try:
        from core.enhanced_order_execution import get_order_executor
        
        executor = get_order_executor()
        if executor:
            print("✅ Order executor initialized")
            
            # Test paper order (won't actually execute)
            print("\nTesting paper order validation...")
            result = executor.validate_order("AAPL", 1, "buy", 150.0)
            if result.get('valid', False):
                print("✅ Order validation working")
            else:
                print(f"❌ Order validation failed: {result.get('error', 'Unknown')}")
        else:
            print("❌ Failed to initialize order executor")
            
    except Exception as e:
        print(f"❌ Order execution error: {e}")
        import traceback
        traceback.print_exc()

def test_automation_engine():
    """Test automation engine."""
    print("\n🤖 TESTING AUTOMATION ENGINE")
    print("=" * 50)
    
    try:
        from core.automation_engine import get_automation_engine
        
        engine = get_automation_engine()
        if engine:
            print("✅ Automation engine initialized")
            print(f"   Status: {'Running' if engine.is_running else 'Stopped'}")
            print(f"   Mode: {engine.mode}")
            print(f"   Active Positions: {len(engine.active_positions)}")
        else:
            print("❌ Failed to initialize automation engine")
            
    except Exception as e:
        print(f"❌ Automation engine error: {e}")
        import traceback
        traceback.print_exc()

def test_ttm_scanner_integration():
    """Test TTM scanner integration with trading."""
    print("\n🔍 TESTING TTM SCANNER INTEGRATION")
    print("=" * 50)
    
    try:
        from scanners.proper_ttm_squeeze_scanner import ProperTTMSqueezeScanner
        
        scanner = ProperTTMSqueezeScanner()
        print("✅ TTM scanner initialized")
        
        # Get a few opportunities
        opportunities = scanner.scan_all_symbols()
        print(f"✅ Found {len(opportunities)} TTM opportunities")
        
        if opportunities:
            top_opp = opportunities[0]
            print(f"   Top opportunity: {top_opp['symbol']} (Grade {top_opp['grade']})")
            
            # Test if we can get trade data for automation
            if 'entry_price' in top_opp and 'target_price' in top_opp:
                print("✅ Trade data available for automation")
                print(f"   Entry: ${top_opp['entry_price']:.2f}")
                print(f"   Target: ${top_opp['target_price']:.2f}")
                print(f"   Stop: ${top_opp['stop_loss']:.2f}")
            else:
                print("❌ Missing trade data (entry/target prices)")
                print(f"   Available keys: {list(top_opp.keys())}")
        
    except Exception as e:
        print(f"❌ TTM scanner integration error: {e}")
        import traceback
        traceback.print_exc()

def test_environment_variables():
    """Test environment variables."""
    print("\n🌍 TESTING ENVIRONMENT VARIABLES")
    print("=" * 50)
    
    # Check trading mode
    live_trading = os.getenv("UTE_LIVE_TRADING", "false").lower()
    print(f"Live Trading Mode: {live_trading}")
    
    # Check if .env file exists
    env_files = [".env", "config/.env", "core/.env"]
    for env_file in env_files:
        if os.path.exists(env_file):
            print(f"✅ Found environment file: {env_file}")
            break
    else:
        print("⚠️ No .env file found - using system environment variables")

def main():
    """Run all diagnostic tests."""
    print("🔧 TOTALRECALL TRADING SYSTEMS DIAGNOSTIC")
    print("=" * 70)
    
    test_environment_variables()
    test_api_keys()
    test_alpaca_connection()
    test_order_execution()
    test_automation_engine()
    test_ttm_scanner_integration()
    
    print("\n" + "=" * 70)
    print("🎯 DIAGNOSTIC COMPLETE")
    print("\nIf you see ❌ errors above, those are likely the cause of")
    print("your auto trading and execution issues.")
    print("\nCommon fixes:")
    print("1. Check API keys in .env file")
    print("2. Verify Alpaca account is active")
    print("3. Check internet connection")
    print("4. Ensure all dependencies are installed")

if __name__ == "__main__":
    main()
