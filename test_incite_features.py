#!/usr/bin/env python3
"""Test Incite AI Style Features

Test the new "make me $X" and market context features.
"""
import sys
import os

# Add all necessary directories to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'core'))
sys.path.insert(0, os.path.join(current_dir, 'gui'))
sys.path.insert(0, os.path.join(current_dir, 'scanners'))
sys.path.insert(0, os.path.join(current_dir, 'trading'))
sys.path.insert(0, os.path.join(current_dir, 'utils'))

def test_profit_targeting():
    """Test the 'make me $X' functionality."""
    print("🎯 Testing TTM Profit Targeting Feature")
    print("=" * 50)
    
    try:
        from chat_core import make_specific_profit_with_ttm
        
        # Test making $50 profit
        result = make_specific_profit_with_ttm(50.0)
        print("📊 RESULT FOR 'MAKE ME $50':")
        print(result)
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing profit targeting: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_market_context():
    """Test market context feature."""
    print("\n📊 Testing Market Context Feature")
    print("=" * 40)
    
    try:
        from chat_core import get_ttm_market_context
        
        context = get_ttm_market_context()
        print("📈 MARKET CONTEXT:")
        print(context)
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing market context: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chat_integration():
    """Test the chat integration with new features."""
    print("\n💬 Testing Chat Integration")
    print("=" * 30)
    
    try:
        from chat_core import chat_gpt
        
        # Test the "make me $50" query
        response = chat_gpt("make me $50 with TTM squeeze trading")
        print("🤖 AI RESPONSE:")
        print(response[:500] + "..." if len(response) > 500 else response)
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing chat integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 TESTING INCITE AI STYLE FEATURES")
    print("=" * 60)
    
    tests = [
        ("Profit Targeting", test_profit_targeting),
        ("Market Context", test_market_context),
        ("Chat Integration", test_chat_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
        except Exception as e:
            print(f"\n❌ {test_name}: EXCEPTION - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL INCITE AI FEATURES WORKING!")
        print("\n🚀 New capabilities added:")
        print("   • 'Make me $X' profit targeting")
        print("   • Market context analysis")
        print("   • TTM-specific intelligence")
        print("   • Incite AI style responses")
        print("\n💬 Try asking the AI:")
        print("   • 'make me $50 with TTM'")
        print("   • 'what's the market context for TTM trading?'")
        print("   • 'find me the best TTM setup right now'")
    else:
        print("⚠️  Some features need attention.")
        print("\n💡 This is normal during development.")
        print("The core functionality should still work.")

if __name__ == "__main__":
    main()
