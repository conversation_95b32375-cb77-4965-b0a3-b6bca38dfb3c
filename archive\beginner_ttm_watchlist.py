"""Beginner-Friendly TTM Squeeze Watchlist

This explains everything in simple terms that any beginner can understand.
No confusing technical jargon - just plain English explanations.
"""
from __future__ import annotations

from ttm_squeeze_watchlist import TTMSqueezeWatchlist
from datetime import datetime


class BeginnerTTMWatchlist:
    """Beginner-friendly TTM Squeeze watchlist with simple explanations."""
    
    def __init__(self):
        self.watchlist = TTMSqueezeWatchlist()
    
    def explain_periods(self, periods: int, timeframe: str) -> str:
        """Convert periods to real time for beginners."""
        if timeframe == '5min':
            minutes = periods * 5
            if minutes < 60:
                return f"{minutes} minutes"
            else:
                hours = minutes / 60
                return f"{hours:.1f} hours"
        elif timeframe == '15min':
            minutes = periods * 15
            if minutes < 60:
                return f"{minutes} minutes"
            else:
                hours = minutes / 60
                return f"{hours:.1f} hours"
        elif timeframe == '1hour':
            hours = periods
            if hours < 24:
                return f"{hours} hours"
            else:
                days = hours / 24
                return f"{days:.1f} days"
        else:
            return f"{periods} time periods"
    
    def explain_breakout_score(self, score: int) -> str:
        """Explain breakout score in simple terms."""
        if score >= 80:
            return "🔥 VERY HIGH - Ready to explode!"
        elif score >= 70:
            return "⚡ HIGH - Building serious pressure"
        elif score >= 60:
            return "📈 MEDIUM - Getting interesting"
        elif score >= 50:
            return "📊 LOW - Early stage"
        else:
            return "😴 VERY LOW - Not ready yet"
    
    def explain_squeeze_stage(self, item: dict) -> str:
        """Explain what stage the squeeze is in."""
        score = item.get('breakout_score', 0)
        duration = item.get('squeeze_duration', 0)
        timeframe = item.get('timeframe', '15min')
        
        time_in_squeeze = self.explain_periods(duration, timeframe)
        
        if score >= 80:
            return f"🚨 READY TO BREAK OUT! (Been building for {time_in_squeeze})"
        elif score >= 70:
            return f"⚡ PRESSURE BUILDING (Been squeezing for {time_in_squeeze})"
        elif score >= 60:
            return f"📈 GETTING READY (In squeeze for {time_in_squeeze})"
        else:
            return f"📊 EARLY STAGE (Just started squeezing - {time_in_squeeze})"
    
    def create_beginner_report(self) -> str:
        """Create a beginner-friendly watchlist report."""
        # Update the watchlist first
        self.watchlist.update_watchlist()
        
        report = "🎯 BEGINNER'S TTM SQUEEZE WATCHLIST\n"
        report += "=" * 50 + "\n\n"
        
        report += "💡 WHAT IS THIS?\n"
        report += "This shows stocks that are 'coiling up' like a spring before they explode.\n"
        report += "We want to catch them BEFORE they explode, not after!\n\n"
        
        # Get top candidates
        candidates = self.watchlist.get_top_breakout_candidates(15)
        
        if candidates:
            report += "🚨 STOCKS READY TO EXPLODE (Watch These!):\n"
            report += "-" * 45 + "\n"
            
            for i, item in enumerate(candidates[:5], 1):
                symbol = item['symbol']
                price = item['price']
                timeframe = item['timeframe']
                score = item['breakout_score']
                
                # Simple explanations
                stage = self.explain_squeeze_stage(item)
                score_meaning = self.explain_breakout_score(score)
                
                report += f"{i}. 📈 {symbol} - ${price:.2f}\n"
                report += f"   Chart: {timeframe} timeframe\n"
                report += f"   Status: {stage}\n"
                report += f"   Readiness: {score_meaning}\n"
                
                # Simple action advice
                if score >= 80:
                    report += f"   💰 ACTION: Watch closely! Could break out any moment!\n"
                elif score >= 70:
                    report += f"   👀 ACTION: Add to watchlist, check every 15-30 minutes\n"
                else:
                    report += f"   📝 ACTION: Keep an eye on it, still building pressure\n"
                
                report += "\n"
        
        # Building pressure stocks
        pressure_stocks = list(self.watchlist.watchlist.get('building_pressure', {}).values())
        if pressure_stocks:
            pressure_stocks.sort(key=lambda x: x.get('breakout_score', 0), reverse=True)
            
            report += "⚡ STOCKS BUILDING PRESSURE (Getting Ready):\n"
            report += "-" * 45 + "\n"
            
            for item in pressure_stocks[:3]:
                symbol = item['symbol']
                price = item['price']
                timeframe = item['timeframe']
                duration = item['squeeze_duration']
                
                time_in_squeeze = self.explain_periods(duration, timeframe)
                
                report += f"📊 {symbol} - ${price:.2f} ({timeframe})\n"
                report += f"   Been squeezing for: {time_in_squeeze}\n"
                report += f"   Status: Pressure is building - not ready yet\n\n"
        
        # Early stage stocks
        early_stocks = list(self.watchlist.watchlist.get('in_squeeze', {}).values())
        if early_stocks:
            early_stocks.sort(key=lambda x: x.get('squeeze_duration', 0), reverse=True)
            
            report += "📋 EARLY STAGE SQUEEZES (Just Starting):\n"
            report += "-" * 40 + "\n"
            
            for item in early_stocks[:3]:
                symbol = item['symbol']
                price = item['price']
                timeframe = item['timeframe']
                duration = item['squeeze_duration']
                
                time_in_squeeze = self.explain_periods(duration, timeframe)
                
                report += f"📝 {symbol} - ${price:.2f} ({timeframe})\n"
                report += f"   Just started squeezing: {time_in_squeeze} ago\n\n"
        
        if not candidates and not pressure_stocks and not early_stocks:
            report += "📊 NO SQUEEZE OPPORTUNITIES RIGHT NOW\n"
            report += "This is normal! The market goes through cycles.\n"
            report += "Check back in 30-60 minutes.\n\n"
        
        # Add beginner explanations
        report += "🎓 BEGINNER'S GUIDE:\n"
        report += "-" * 20 + "\n"
        report += "🔥 READY TO EXPLODE = Stock could break out any moment\n"
        report += "⚡ BUILDING PRESSURE = Getting close, watch closely\n"
        report += "📈 GETTING READY = Still building up, be patient\n"
        report += "📊 EARLY STAGE = Just started, too early to trade\n\n"
        
        report += "💰 TRADING STRATEGY:\n"
        report += "-" * 20 + "\n"
        report += "1. Focus on 'READY TO EXPLODE' stocks\n"
        report += "2. Set price alerts just above current price\n"
        report += "3. When it breaks out, enter the trade\n"
        report += "4. Set stop loss 2-3% below entry\n"
        report += "5. Take profit at 5-8% gain\n\n"
        
        report += "⚠️ IMPORTANT REMINDERS:\n"
        report += "-" * 25 + "\n"
        report += "• Not all squeezes lead to big moves\n"
        report += "• Always use stop losses\n"
        report += "• Start with small position sizes\n"
        report += "• Practice with paper trading first\n\n"
        
        report += f"🕒 Last Updated: {datetime.now().strftime('%I:%M %p')}\n"
        
        return report


def run_beginner_watchlist() -> str:
    """Run the beginner-friendly watchlist."""
    try:
        beginner_watchlist = BeginnerTTMWatchlist()
        return beginner_watchlist.create_beginner_report()
    except Exception as e:
        return f"❌ Error creating beginner watchlist: {str(e)}"


if __name__ == "__main__":
    print("🎓 BEGINNER'S TTM SQUEEZE WATCHLIST")
    print("=" * 50)
    
    result = run_beginner_watchlist()
    print(result)
    
    print("\n" + "=" * 50)
    print("💡 REMEMBER:")
    print("• 'Periods' = Time bars on the chart")
    print("• 5min chart: 10 periods = 50 minutes")
    print("• 15min chart: 10 periods = 2.5 hours")
    print("• 1hour chart: 10 periods = 10 hours")
    print("• Focus on stocks 'READY TO EXPLODE'!")
