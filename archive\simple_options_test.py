#!/usr/bin/env python3
"""Simple test of options functionality."""

from options_strategies import get_options_strategy_recommendation

def test_simple_options():
    """Test the options functionality directly."""
    print("🧪 Simple Options Test")
    print("=" * 30)
    
    try:
        # Test a simple options recommendation
        result = get_options_strategy_recommendation("AAPL", "bullish", "moderate")
        
        print("✅ Options system working!")
        print(f"Strategy: {result['strategy']}")
        print(f"Recommendation: {result['recommendation']}")
        print(f"Max Profit: ${result['max_profit']}")
        print(f"Max Loss: ${result['max_loss']}")
        print(f"Risk/Reward: {result['risk_reward_ratio']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_options()
    if success:
        print("\n🎉 Options system is ready for use!")
        print("\n📋 What you can do now:")
        print("1. 🚀 Launch the chat interface: python launch_ultimate_expert_v2.py")
        print("2. 💬 Ask questions like:")
        print("   • 'What options strategy for AAPL if bullish?'")
        print("   • 'Give me bearish options for TSLA'")
        print("   • 'Scan for best options opportunities'")
        print("3. 📊 Use the profit target feature")
        print("4. 🔍 Get comprehensive market analysis")
    else:
        print("\n❌ Options system needs troubleshooting")
