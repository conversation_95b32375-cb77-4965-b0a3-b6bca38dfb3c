#!/usr/bin/env python3
"""Debug the probability calculation for SPY bull call spread."""

from options_strategies import OptionsStrategies

def debug_spy_probability():
    """Debug why SPY bull call spread has only 15% probability."""
    
    print("=== DEBUGGING SPY BULL CALL SPREAD PROBABILITY ===")
    
    options = OptionsStrategies()
    result = options.bull_call_spread('SPY')
    
    print(f"Strategy: {result.strategy_name}")
    print(f"Max Profit: ${result.max_profit:.2f}")
    print(f"Max Loss: ${result.max_loss:.2f}")
    print(f"Breakeven Points: {result.breakeven_points}")
    print(f"Probability: {result.probability_of_profit:.1%}")
    print(f"Net Premium: ${result.net_premium:.2f}")
    
    if result.contracts:
        print(f"\nContracts:")
        for i, contract in enumerate(result.contracts):
            print(f"  {i+1}. {contract.option_type.upper()} Strike: ${contract.strike}, Price: ${contract.price:.2f}")
    
    # Test current SPY price
    stock_price = options._get_stock_price('SPY')
    print(f"\nCurrent SPY Price: ${stock_price:.2f}")
    
    # Check if current price is above breakeven
    if result.breakeven_points:
        breakeven = result.breakeven_points[0]
        print(f"Breakeven: ${breakeven:.2f}")
        print(f"Current price above breakeven: {stock_price > breakeven}")
        print(f"Difference: ${stock_price - breakeven:.2f}")
        
        # This should be profitable if SPY is already above breakeven!
        if stock_price > breakeven:
            print("🚨 ERROR: SPY is already above breakeven, probability should be much higher!")
            print("The Monte Carlo simulation is likely using wrong parameters.")
    
    print("\n=== ANALYSIS ===")
    print("If SPY is at $590 and breakeven is $587.14, then:")
    print("- SPY is already $2.86 above breakeven")
    print("- The trade is already profitable at current levels")
    print("- Probability should be 50%+ since SPY just needs to stay flat or go up")
    print("- 15% probability suggests the simulation thinks SPY will drop significantly")

if __name__ == "__main__":
    debug_spy_probability()
