#!/usr/bin/env python3
"""TTM Trading Automation Engine

Smart automation system for TTM trading with multiple modes:
- Conservative Mode: Grade A+ only, tight risk management
- Balanced Mode: Grade A/A+ setups, standard risk parameters
- Manual Override: Complete user control
- Emergency Stop: Instant halt capability
"""
import threading
import time
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Callable
from enum import Enum
import logging


class AutomationMode(Enum):
    """Automation mode settings."""
    MANUAL = "manual"
    CONSERVATIVE = "conservative"
    BALANCED = "balanced"
    STOPPED = "stopped"


class AutomationEngine:
    """Smart TTM trading automation engine."""
    
    def __init__(self):
        self.is_running = False
        self.automation_thread = None
        self.mode = AutomationMode.MANUAL
        self.emergency_stop = False

        # Configuration
        self.scan_interval = 60  # seconds between scans
        self.max_daily_loss = 500  # max daily loss in dollars
        self.max_positions = 3  # max concurrent positions
        self.daily_pnl = 0.0
        self.active_positions = {}

        # Setup logging first
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Initialize safety and alerting systems
        self.safety_system = None
        self.alerting_system = None
        self.order_executor = None
        self._init_safety_systems()

        # Mode configurations
        self.mode_configs = {
            AutomationMode.CONSERVATIVE: {
                "min_grade": "A+",
                "min_confidence": 90,
                "max_position_risk": 1.0,  # 1% of account per trade
                "stop_loss_pct": 2.0,  # 2% stop loss
                "take_profit_ratio": 3.0,  # 3:1 reward:risk
                "max_positions": 2,
                "require_sentiment": True,
                "require_options_flow": True
            },
            AutomationMode.BALANCED: {
                "min_grade": "A",
                "min_confidence": 80,
                "max_position_risk": 2.0,  # 2% of account per trade
                "stop_loss_pct": 3.0,  # 3% stop loss
                "take_profit_ratio": 2.5,  # 2.5:1 reward:risk
                "max_positions": 3,
                "require_sentiment": True,
                "require_options_flow": False
            }
        }
        
        # Callbacks for notifications
        self.callbacks = {
            'trade_executed': [],
            'position_closed': [],
            'daily_limit_hit': [],
            'emergency_stop': [],
            'mode_changed': []
        }

    def _init_safety_systems(self):
        """Initialize safety and alerting systems."""
        try:
            from safety_system import get_safety_system
            from advanced_alerting import get_alerting_system
            from enhanced_order_execution import get_order_executor

            self.safety_system = get_safety_system()
            self.alerting_system = get_alerting_system()
            self.order_executor = get_order_executor()

            # Register alert callback for safety events
            self.safety_system.register_alert_callback(self._handle_safety_alert)

            self.logger.info("Safety systems initialized")

        except Exception as e:
            self.logger.error(f"Error initializing safety systems: {e}")
            # Continue without safety systems for backward compatibility
    
    def start_automation(self, mode: AutomationMode) -> str:
        """Start the automation engine in specified mode."""
        if self.is_running:
            return "⚠️ Automation already running"
        
        if mode == AutomationMode.MANUAL:
            return "⚠️ Cannot start automation in manual mode"
        
        self.mode = mode
        self.is_running = True
        self.emergency_stop = False
        self.daily_pnl = 0.0
        
        # Start automation thread
        self.automation_thread = threading.Thread(target=self._automation_loop, daemon=True)
        self.automation_thread.start()
        
        mode_name = mode.value.title()
        self.logger.info(f"Automation started in {mode_name} mode")
        
        # Trigger callbacks
        for callback in self.callbacks['mode_changed']:
            try:
                callback(f"Started {mode_name} mode")
            except:
                pass
        
        return f"🤖 **AUTOMATION STARTED**\n\n**Mode:** {mode_name}\n**Settings:** {self._get_mode_description(mode)}"
    
    def stop_automation(self) -> str:
        """Stop the automation engine."""
        if not self.is_running:
            return "⚠️ Automation not running"
        
        self.is_running = False
        self.mode = AutomationMode.MANUAL
        
        if self.automation_thread:
            self.automation_thread.join(timeout=5)
        
        self.logger.info("Automation stopped")
        
        # Trigger callbacks
        for callback in self.callbacks['mode_changed']:
            try:
                callback("Automation stopped")
            except:
                pass
        
        return "⏹️ **AUTOMATION STOPPED**\n\nReturned to manual trading mode."
    
    def emergency_stop_all(self) -> str:
        """Emergency stop - halt all automation immediately."""
        self.emergency_stop = True
        self.is_running = False
        self.mode = AutomationMode.STOPPED
        
        # Close all automated positions (would integrate with broker)
        positions_closed = len(self.active_positions)
        self.active_positions.clear()
        
        self.logger.warning("EMERGENCY STOP activated")
        
        # Trigger callbacks
        for callback in self.callbacks['emergency_stop']:
            try:
                callback("Emergency stop activated")
            except:
                pass
        
        return f"🚨 **EMERGENCY STOP ACTIVATED**\n\n• All automation halted\n• {positions_closed} positions flagged for review\n• Manual intervention required"
    
    def change_mode(self, new_mode: AutomationMode) -> str:
        """Change automation mode while running."""
        if not self.is_running and new_mode != AutomationMode.MANUAL:
            return "⚠️ Start automation first before changing modes"
        
        old_mode = self.mode
        self.mode = new_mode
        
        if new_mode == AutomationMode.MANUAL:
            return self.stop_automation()
        
        mode_name = new_mode.value.title()
        self.logger.info(f"Mode changed from {old_mode.value} to {new_mode.value}")
        
        # Trigger callbacks
        for callback in self.callbacks['mode_changed']:
            try:
                callback(f"Changed to {mode_name} mode")
            except:
                pass
        
        return f"🔄 **MODE CHANGED**\n\n**New Mode:** {mode_name}\n**Settings:** {self._get_mode_description(new_mode)}"
    
    def get_automation_status(self) -> Dict:
        """Get current automation status."""
        return {
            "is_running": self.is_running,
            "mode": self.mode.value,
            "emergency_stop": self.emergency_stop,
            "daily_pnl": self.daily_pnl,
            "active_positions": len(self.active_positions),
            "max_positions": self.mode_configs.get(self.mode, {}).get("max_positions", 0),
            "daily_loss_limit": self.max_daily_loss,
            "remaining_loss_buffer": self.max_daily_loss + self.daily_pnl if self.daily_pnl < 0 else self.max_daily_loss
        }
    
    def register_callback(self, event_type: str, callback: Callable):
        """Register callback for automation events."""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
    
    def _automation_loop(self):
        """Main automation loop."""
        while self.is_running and not self.emergency_stop:
            try:
                # Check daily loss limit
                if self.daily_pnl <= -self.max_daily_loss:
                    self._trigger_daily_limit_stop()
                    break
                
                # Check if we can take new positions
                config = self.mode_configs.get(self.mode, {})
                max_pos = config.get("max_positions", 0)
                
                if len(self.active_positions) < max_pos:
                    # Scan for new opportunities
                    self._scan_for_opportunities()
                
                # Monitor existing positions
                self._monitor_positions()
                
                # Wait before next scan
                time.sleep(self.scan_interval)
                
            except Exception as e:
                self.logger.error(f"Automation loop error: {e}")
                time.sleep(self.scan_interval)
    
    def _scan_for_opportunities(self):
        """Scan for new TTM trading opportunities."""
        try:
            # This would integrate with your TTM scanner
            # For now, we'll simulate finding opportunities
            
            config = self.mode_configs.get(self.mode, {})
            
            # Mock opportunity detection
            import random
            if random.random() < 0.1:  # 10% chance of finding opportunity
                
                # Mock setup data
                setup = {
                    "symbol": random.choice(["AAPL", "NVDA", "TSLA", "AMD", "MSFT"]),
                    "grade": random.choice(["A+", "A", "B+"]),
                    "confidence": random.uniform(75, 95),
                    "entry_price": random.uniform(100, 300),
                    "sentiment_score": random.uniform(-0.5, 0.8),
                    "options_flow": random.choice(["bullish", "neutral", "bearish"])
                }
                
                # Check if setup meets criteria
                if self._evaluate_setup(setup, config):
                    self._execute_automated_trade(setup, config)
                    
        except Exception as e:
            self.logger.error(f"Opportunity scan error: {e}")
    
    def _evaluate_setup(self, setup: Dict, config: Dict) -> bool:
        """Evaluate if a setup meets automation criteria."""
        # Check grade requirement
        grade_hierarchy = {"A+": 3, "A": 2, "B+": 1, "B": 0}
        min_grade = config.get("min_grade", "A+")
        
        if grade_hierarchy.get(setup["grade"], 0) < grade_hierarchy.get(min_grade, 3):
            return False
        
        # Check confidence
        if setup["confidence"] < config.get("min_confidence", 90):
            return False
        
        # Check sentiment if required
        if config.get("require_sentiment", False):
            if setup["sentiment_score"] < 0.2:  # Require bullish sentiment
                return False
        
        # Check options flow if required
        if config.get("require_options_flow", False):
            if setup["options_flow"] != "bullish":
                return False
        
        return True
    
    def _execute_automated_trade(self, setup: Dict, config: Dict):
        """Execute an automated trade based on setup using enhanced order execution."""
        try:
            symbol = setup["symbol"]
            entry_price = setup["entry_price"]

            # Calculate position size
            account_size = 10000  # Mock account size
            risk_per_trade = config.get("max_position_risk", 1.0) / 100
            stop_loss_pct = config.get("stop_loss_pct", 2.0) / 100
            take_profit_ratio = config.get("take_profit_ratio", 3.0)

            risk_amount = account_size * risk_per_trade
            stop_loss_price = entry_price * (1 - stop_loss_pct)
            risk_per_share = entry_price - stop_loss_price

            if risk_per_share > 0:
                quantity = int(risk_amount / risk_per_share)

                # Calculate take profit percentage
                take_profit_pct = stop_loss_pct * take_profit_ratio

                # Execute bracket order using enhanced order executor
                if self.order_executor:
                    result = self.order_executor.execute_bracket_order(
                        symbol=symbol,
                        quantity=quantity,
                        side="buy",
                        entry_price=entry_price,
                        stop_loss_pct=stop_loss_pct * 100,  # Convert to percentage
                        take_profit_pct=take_profit_pct * 100
                    )

                    if result["success"]:
                        # Create position record
                        position_id = result["order_id"]
                        position = {
                            "symbol": symbol,
                            "entry_price": entry_price,
                            "quantity": quantity,
                            "stop_loss": result["stop_price"],
                            "take_profit": result["target_price"],
                            "entry_time": datetime.now(),
                            "mode": self.mode.value,
                            "setup_grade": setup["grade"],
                            "confidence": setup["confidence"],
                            "order_id": position_id
                        }

                        self.active_positions[position_id] = position

                        # Send alert
                        if self.alerting_system:
                            self.alerting_system.send_alert(
                                "TRADE_EXECUTED",
                                f"Automated trade: {symbol} {quantity} shares @ ${entry_price:.2f}",
                                "MEDIUM",
                                {"symbol": symbol, "quantity": quantity, "price": entry_price}
                            )

                        # Log the trade
                        self.logger.info(f"Automated bracket order executed: {symbol} {quantity} shares @ ${entry_price:.2f}")

                        # Trigger callbacks
                        trade_info = f"🤖 Auto-executed {symbol}: {quantity} shares @ ${entry_price:.2f} (Bracket Order)"
                        for callback in self.callbacks['trade_executed']:
                            try:
                                callback(trade_info)
                            except:
                                pass
                    else:
                        self.logger.error(f"Bracket order failed for {symbol}: {result.get('error', 'Unknown error')}")

                        # Send failure alert
                        if self.alerting_system:
                            self.alerting_system.send_alert(
                                "TRADE_FAILED",
                                f"Failed to execute {symbol}: {result.get('error', 'Unknown error')}",
                                "HIGH"
                            )
                else:
                    # Fallback to old method if order executor not available
                    self._execute_legacy_trade(symbol, quantity, entry_price, stop_loss_price,
                                             entry_price + (risk_per_share * take_profit_ratio), setup, config)

        except Exception as e:
            self.logger.error(f"Trade execution error: {e}")

            # Send critical alert
            if self.alerting_system:
                self.alerting_system.send_alert(
                    "EXECUTION_ERROR",
                    f"Critical error executing trade: {str(e)}",
                    "CRITICAL"
                )
    
    def _monitor_positions(self):
        """Monitor existing automated positions."""
        positions_to_close = []
        
        for pos_id, position in self.active_positions.items():
            try:
                # This would get current price from broker/data feed
                # For now, simulate price movement
                import random
                current_price = position["entry_price"] * random.uniform(0.95, 1.08)
                
                symbol = position["symbol"]
                
                # Check stop loss
                if current_price <= position["stop_loss"]:
                    pnl = (position["stop_loss"] - position["entry_price"]) * position["quantity"]
                    self.daily_pnl += pnl
                    positions_to_close.append(pos_id)
                    
                    self.logger.info(f"Stop loss hit: {symbol} @ ${current_price:.2f}")
                    
                    # Trigger callback
                    close_info = f"🛑 Stop loss: {symbol} @ ${current_price:.2f} (${pnl:.2f})"
                    for callback in self.callbacks['position_closed']:
                        try:
                            callback(close_info)
                        except:
                            pass
                
                # Check take profit
                elif current_price >= position["take_profit"]:
                    pnl = (position["take_profit"] - position["entry_price"]) * position["quantity"]
                    self.daily_pnl += pnl
                    positions_to_close.append(pos_id)
                    
                    self.logger.info(f"Take profit hit: {symbol} @ ${current_price:.2f}")
                    
                    # Trigger callback
                    close_info = f"🎯 Take profit: {symbol} @ ${current_price:.2f} (${pnl:.2f})"
                    for callback in self.callbacks['position_closed']:
                        try:
                            callback(close_info)
                        except:
                            pass
                
            except Exception as e:
                self.logger.error(f"Position monitoring error: {e}")
        
        # Remove closed positions
        for pos_id in positions_to_close:
            del self.active_positions[pos_id]
    
    def _trigger_daily_limit_stop(self):
        """Trigger daily loss limit stop."""
        self.is_running = False
        self.mode = AutomationMode.STOPPED
        
        self.logger.warning(f"Daily loss limit hit: ${self.daily_pnl:.2f}")
        
        # Trigger callbacks
        limit_info = f"🚨 Daily loss limit hit: ${self.daily_pnl:.2f}"
        for callback in self.callbacks['daily_limit_hit']:
            try:
                callback(limit_info)
            except:
                pass
    
    def _get_mode_description(self, mode: AutomationMode) -> str:
        """Get description of automation mode settings."""
        if mode not in self.mode_configs:
            return "Manual mode - no automation"

        config = self.mode_configs[mode]
        return f"Min Grade: {config['min_grade']}, Risk: {config['max_position_risk']}%, Stop: {config['stop_loss_pct']}%"

    def _handle_safety_alert(self, alert_data: Dict):
        """Handle safety system alerts."""
        try:
            alert_type = alert_data.get("type", "")
            message = alert_data.get("message", "")
            severity = alert_data.get("severity", "MEDIUM")

            # Forward to alerting system
            if self.alerting_system:
                self.alerting_system.send_alert(f"SAFETY_{alert_type}", message, severity)

            # Handle critical safety events
            if alert_type == "LOCKOUT":
                self.emergency_stop_all()

            self.logger.warning(f"Safety alert: {message}")

        except Exception as e:
            self.logger.error(f"Error handling safety alert: {e}")

    def _execute_legacy_trade(self, symbol: str, quantity: int, entry_price: float,
                            stop_loss_price: float, take_profit_price: float,
                            setup: Dict, config: Dict):
        """Legacy trade execution method (fallback)."""
        try:
            # Create position record (old method)
            position_id = f"{symbol}_{int(time.time())}"
            position = {
                "symbol": symbol,
                "entry_price": entry_price,
                "quantity": quantity,
                "stop_loss": stop_loss_price,
                "take_profit": take_profit_price,
                "entry_time": datetime.now(),
                "mode": self.mode.value,
                "setup_grade": setup["grade"],
                "confidence": setup["confidence"]
            }

            self.active_positions[position_id] = position

            # Log the trade
            self.logger.info(f"Legacy automated trade executed: {symbol} {quantity} shares @ ${entry_price:.2f}")

            # Trigger callbacks
            trade_info = f"🤖 Auto-executed {symbol}: {quantity} shares @ ${entry_price:.2f} (Legacy)"
            for callback in self.callbacks['trade_executed']:
                try:
                    callback(trade_info)
                except:
                    pass

        except Exception as e:
            self.logger.error(f"Legacy trade execution error: {e}")


# Global automation engine instance
_automation_engine = None

def get_automation_engine() -> AutomationEngine:
    """Get the global automation engine instance."""
    global _automation_engine
    if _automation_engine is None:
        _automation_engine = AutomationEngine()
    return _automation_engine


if __name__ == "__main__":
    # Test the automation engine
    engine = AutomationEngine()
    
    print("🧪 Testing Automation Engine")
    print("=" * 40)
    
    # Test starting automation
    result = engine.start_automation(AutomationMode.CONSERVATIVE)
    print(f"✅ Start conservative: {result[:50]}...")
    
    # Test status
    status = engine.get_automation_status()
    print(f"✅ Status: Running={status['is_running']}, Mode={status['mode']}")
    
    # Test mode change
    result = engine.change_mode(AutomationMode.BALANCED)
    print(f"✅ Change to balanced: {result[:50]}...")
    
    # Test stop
    result = engine.stop_automation()
    print(f"✅ Stop automation: {result[:50]}...")
    
    # Test emergency stop
    result = engine.emergency_stop_all()
    print(f"✅ Emergency stop: {result[:50]}...")
