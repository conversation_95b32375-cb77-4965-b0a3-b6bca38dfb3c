"""Universal Market Intelligence System

This system can access ALL FMP endpoints to answer ANY market question.
With paid tier access, we can query hundreds of endpoints dynamically
based on the question asked.

Examples of complex questions it can handle:
- "What insider trades are recent and close to current price?"
- "Show me companies with earnings beats and institutional buying"
- "Find stocks with unusual options activity and analyst upgrades"
- "What are the latest SEC filings for tech companies?"
- "Show me dividend aristocrats with strong cash flow"
- "Find IPOs with high institutional ownership"
"""
from __future__ import annotations

import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import re

from config import get_api_key
from logger_util import info, warning

FMP_BASE = "https://financialmodelingprep.com/api/v3"
FMP_V4_BASE = "https://financialmodelingprep.com/api/v4"


class UniversalMarketIntelligence:
    """Universal system that can access all FMP endpoints to answer any question."""
    
    def __init__(self):
        self.api_key = get_api_key('FMP_API_KEY')
        if not self.api_key:
            raise ValueError("FMP_API_KEY not found in environment")
        
        # Comprehensive endpoint registry for paid tier
        self.endpoints = self._build_endpoint_registry()
    
    def _build_endpoint_registry(self) -> Dict[str, Dict]:
        """Build comprehensive registry of all FMP endpoints."""
        return {
            # Market Data & Quotes
            "quote": {"url": "/quote/{symbol}", "description": "Real-time stock quotes"},
            "quotes": {"url": "/quotes/{exchange}", "description": "All quotes for exchange"},
            "historical_price": {"url": "/historical-price-full/{symbol}", "description": "Historical prices"},
            "historical_chart": {"url": "/historical-chart/{timeframe}/{symbol}", "description": "Intraday charts"},
            
            # Company Fundamentals
            "company_profile": {"url": "/profile/{symbol}", "description": "Company profile and metrics"},
            "key_metrics": {"url": "/key-metrics/{symbol}", "description": "Key financial metrics"},
            "financial_ratios": {"url": "/ratios/{symbol}", "description": "Financial ratios"},
            "enterprise_values": {"url": "/enterprise-values/{symbol}", "description": "Enterprise value metrics"},
            "company_outlook": {"url": "/company-outlook", "description": "Company outlook and profile"},
            
            # Financial Statements
            "income_statement": {"url": "/income-statement/{symbol}", "description": "Income statements"},
            "balance_sheet": {"url": "/balance-sheet-statement/{symbol}", "description": "Balance sheets"},
            "cash_flow": {"url": "/cash-flow-statement/{symbol}", "description": "Cash flow statements"},
            "financial_growth": {"url": "/financial-growth/{symbol}", "description": "Financial growth metrics"},
            
            # Insider Trading & Institutional
            "insider_trading": {"url": "/insider-trading", "description": "Insider trading transactions"},
            "insider_trading_rss": {"url": "/insider-trading-rss-feed", "description": "Real-time insider trading feed"},
            "institutional_holdings": {"url": "/institutional-holder/{symbol}", "description": "Institutional holdings"},
            "mutual_fund_holdings": {"url": "/mutual-fund-holder/{symbol}", "description": "Mutual fund holdings"},
            "etf_holdings": {"url": "/etf-holder/{symbol}", "description": "ETF holdings"},
            "form_13f": {"url": "/form-thirteen/{cik}", "description": "13F institutional filings"},
            "senate_trading": {"url": "/senate-trading", "description": "Senate trading disclosures"},
            "house_trading": {"url": "/house-trading", "description": "House trading disclosures"},
            
            # Earnings & Estimates
            "earnings_calendar": {"url": "/earning_calendar", "description": "Earnings calendar"},
            "earnings_confirmed": {"url": "/earnings-confirmed", "description": "Confirmed earnings dates"},
            "earnings_surprises": {"url": "/earnings-surprises/{symbol}", "description": "Earnings surprises"},
            "analyst_estimates": {"url": "/analyst-estimates/{symbol}", "description": "Analyst estimates"},
            "analyst_recommendations": {"url": "/analyst-stock-recommendations/{symbol}", "description": "Analyst recommendations"},
            "price_targets": {"url": "/price-target/{symbol}", "description": "Analyst price targets"},
            "upgrades_downgrades": {"url": "/upgrades-downgrades", "description": "Analyst upgrades/downgrades"},
            
            # Dividends & Corporate Actions
            "dividend_calendar": {"url": "/stock_dividend_calendar", "description": "Dividend calendar"},
            "dividend_history": {"url": "/historical-price-full/stock_dividend/{symbol}", "description": "Dividend history"},
            "stock_split_calendar": {"url": "/stock_split_calendar", "description": "Stock split calendar"},
            "stock_split_history": {"url": "/historical-price-full/stock_split/{symbol}", "description": "Stock split history"},
            
            # IPOs & New Listings
            "ipo_calendar": {"url": "/ipo_calendar", "description": "IPO calendar"},
            "ipo_calendar_confirmed": {"url": "/ipo-calendar-confirmed", "description": "Confirmed IPO calendar"},
            "ipo_calendar_prospectus": {"url": "/ipo-calendar-prospectus", "description": "IPO prospectus calendar"},
            
            # Options Data
            "options_chain": {"url": "/options-chain/{symbol}", "description": "Options chain"},
            "options_contracts": {"url": "/options-contracts/{symbol}", "description": "Options contracts"},
            "options_statistics": {"url": "/options-statistics/{symbol}", "description": "Options statistics"},
            
            # Economic Data
            "economic_calendar": {"url": "/economic_calendar", "description": "Economic calendar"},
            "market_hours": {"url": "/market-hours", "description": "Market hours"},
            "market_open": {"url": "/is-the-market-open", "description": "Market open status"},
            
            # Screening & Discovery
            "stock_screener": {"url": "/stock-screener", "description": "Stock screener"},
            "delisted_companies": {"url": "/delisted-companies", "description": "Delisted companies"},
            "symbols_list": {"url": "/stock/list", "description": "All available symbols"},
            "etf_list": {"url": "/etf/list", "description": "All ETFs"},
            "mutual_fund_list": {"url": "/mutual-fund/list", "description": "All mutual funds"},
            
            # News & Sentiment
            "stock_news": {"url": "/stock_news", "description": "Stock news"},
            "general_news": {"url": "/general_news", "description": "General market news"},
            "press_releases": {"url": "/press-releases/{symbol}", "description": "Company press releases"},
            
            # SEC Filings
            "sec_filings": {"url": "/sec_filings/{symbol}", "description": "SEC filings"},
            "rss_feed": {"url": "/rss_feed", "description": "RSS feed"},
            
            # Market Performance
            "sector_performance": {"url": "/sectors-performance", "description": "Sector performance"},
            "gainers": {"url": "/gainers", "description": "Top gainers"},
            "losers": {"url": "/losers", "description": "Top losers"},
            "most_active": {"url": "/actives", "description": "Most active stocks"},
            
            # International Markets
            "forex": {"url": "/fx", "description": "Forex rates"},
            "crypto": {"url": "/quote/{crypto}", "description": "Cryptocurrency quotes"},
            "commodities": {"url": "/quote/{commodity}", "description": "Commodity prices"},
            
            # Advanced Analytics (V4 endpoints)
            "social_sentiment": {"url": "/social-sentiment", "base": FMP_V4_BASE, "description": "Social media sentiment"},
            "commitment_of_traders": {"url": "/commitment_of_traders_report", "base": FMP_V4_BASE, "description": "COT reports"},
        }
    
    def _fmp_get(self, endpoint: str, params: Dict = None, base_url: str = FMP_BASE) -> Any:
        """Make FMP API request with comprehensive error handling."""
        try:
            url = f"{base_url}{endpoint}"
            
            # Add API key to params
            if params is None:
                params = {}
            params['apikey'] = self.api_key
            
            response = requests.get(url, params=params, timeout=15)
            
            if response.status_code == 200:
                return response.json()
            else:
                warning(f"FMP API error {response.status_code}: {response.text[:200]}")
                return []
                
        except Exception as e:
            warning(f"FMP API request failed: {e}")
            return []
    
    def analyze_question(self, question: str) -> List[str]:
        """Analyze question to determine which endpoints to query."""
        question_lower = question.lower()
        relevant_endpoints = []
        
        # Keyword mapping to endpoints
        keyword_mappings = {
            # Insider trading keywords
            r'insider|inside.*trad|insider.*buy|insider.*sell': ['insider_trading', 'insider_trading_rss'],
            r'institutional|13f|mutual.*fund|etf.*hold': ['institutional_holdings', 'mutual_fund_holdings', 'etf_holdings'],
            r'senate.*trad|congress|politician': ['senate_trading', 'house_trading'],
            
            # Earnings keywords
            r'earning|eps|beat|miss|surprise': ['earnings_calendar', 'earnings_surprises', 'earnings_confirmed'],
            r'analyst|estimate|recommend|upgrade|downgrade|price.*target': ['analyst_estimates', 'analyst_recommendations', 'price_targets', 'upgrades_downgrades'],
            
            # Financial keywords
            r'financial|income|balance.*sheet|cash.*flow|revenue|profit': ['income_statement', 'balance_sheet', 'cash_flow', 'financial_growth'],
            r'ratio|metric|valuation|pe.*ratio|debt': ['financial_ratios', 'key_metrics', 'enterprise_values'],
            
            # Market activity keywords
            r'volume|unusual.*volume|active': ['most_active', 'quote'],
            r'gainer|loser|performance|mover': ['gainers', 'losers', 'sector_performance'],
            
            # Options keywords
            r'option|call|put|strike|expir': ['options_chain', 'options_contracts', 'options_statistics'],
            
            # Dividend keywords
            r'dividend|yield|payout|ex.*date': ['dividend_calendar', 'dividend_history'],
            
            # IPO keywords
            r'ipo|new.*list|debut': ['ipo_calendar', 'ipo_calendar_confirmed', 'ipo_calendar_prospectus'],
            
            # News keywords
            r'news|press.*release|announcement': ['stock_news', 'general_news', 'press_releases'],
            
            # SEC filings keywords
            r'sec.*filing|10k|10q|8k|filing': ['sec_filings'],
            
            # Price keywords
            r'price|quote|current.*price|stock.*price': ['quote', 'historical_price'],
        }
        
        # Find relevant endpoints based on keywords
        for pattern, endpoints in keyword_mappings.items():
            if re.search(pattern, question_lower):
                relevant_endpoints.extend(endpoints)
        
        # Remove duplicates while preserving order
        return list(dict.fromkeys(relevant_endpoints))
    
    def answer_complex_question(self, question: str) -> str:
        """Answer any complex market question using appropriate endpoints."""
        info(f"🧠 Analyzing complex question: {question}")
        
        # Determine which endpoints to query
        relevant_endpoints = self.analyze_question(question)
        
        if not relevant_endpoints:
            return "❌ I couldn't determine which market data to analyze for your question. Please be more specific about what you're looking for."
        
        info(f"🔍 Using endpoints: {relevant_endpoints}")
        
        # Query relevant endpoints
        results = {}
        for endpoint_name in relevant_endpoints:
            if endpoint_name in self.endpoints:
                endpoint_info = self.endpoints[endpoint_name]
                
                try:
                    # Handle different endpoint types
                    if '{symbol}' in endpoint_info['url']:
                        # Skip symbol-specific endpoints for now, or use popular symbols
                        continue
                    else:
                        # General market endpoints
                        base_url = endpoint_info.get('base', FMP_BASE)
                        data = self._fmp_get(endpoint_info['url'], base_url=base_url)
                        if data:
                            results[endpoint_name] = data
                            
                except Exception as e:
                    warning(f"Error querying {endpoint_name}: {e}")
                    continue
        
        # Format comprehensive response
        return self._format_comprehensive_response(question, results)
    
    def _format_comprehensive_response(self, question: str, results: Dict) -> str:
        """Format comprehensive response from multiple data sources."""
        if not results:
            return "📊 No relevant market data found for your question. The market might be closed or the specific data might not be available."
        
        response = f"🎯 COMPREHENSIVE MARKET ANALYSIS\n"
        response += f"Question: {question}\n"
        response += "=" * 60 + "\n\n"
        
        # Process each data source
        for endpoint_name, data in results.items():
            endpoint_info = self.endpoints.get(endpoint_name, {})
            description = endpoint_info.get('description', endpoint_name)
            
            response += f"📊 {description.upper()}:\n"
            response += "-" * 40 + "\n"
            
            if isinstance(data, list) and data:
                # Show first few items from list data
                for i, item in enumerate(data[:5]):
                    if isinstance(item, dict):
                        # Extract key information
                        key_fields = ['symbol', 'name', 'price', 'change', 'volume', 'date', 'filingDate', 'transactionType']
                        item_info = []
                        for field in key_fields:
                            if field in item and item[field]:
                                item_info.append(f"{field}: {item[field]}")
                        
                        if item_info:
                            response += f"  {i+1}. {' | '.join(item_info[:3])}\n"
                
                if len(data) > 5:
                    response += f"  ... and {len(data) - 5} more items\n"
                    
            elif isinstance(data, dict):
                # Show key-value pairs for dict data
                for key, value in list(data.items())[:5]:
                    response += f"  {key}: {value}\n"
            
            response += "\n"
        
        response += "💡 This analysis combines multiple market data sources to provide comprehensive insights.\n"
        response += "📞 For more specific analysis, please ask about particular symbols or timeframes.\n"
        
        return response


def answer_any_market_question(question: str) -> str:
    """Answer any market question using universal intelligence."""
    try:
        intelligence = UniversalMarketIntelligence()
        return intelligence.answer_complex_question(question)
    except Exception as e:
        return f"❌ Error analyzing market question: {str(e)}"


if __name__ == "__main__":
    print("🧠 UNIVERSAL MARKET INTELLIGENCE SYSTEM")
    print("=" * 60)
    
    # Test complex questions
    test_questions = [
        "What insider trades are there that are recent and are close to their current price?",
        "Show me the latest earnings surprises and analyst upgrades",
        "What are the top gainers with unusual volume today?",
        "Find companies with recent SEC filings and institutional buying"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}️⃣ TESTING: {question}")
        print("-" * 50)
        result = answer_any_market_question(question)
        print(result[:500] + "..." if len(result) > 500 else result)
        print("\n" + "=" * 60)
