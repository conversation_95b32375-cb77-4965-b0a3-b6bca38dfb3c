#!/usr/bin/env python3
"""
Trade Risk/Reward Analyzer
Provides specific answers about trade risk, reward, and direction
"""
import sys
import os

# Add paths
sys.path.insert(0, os.getcwd())
sys.path.insert(0, os.path.join(os.getcwd(), 'core'))
sys.path.insert(0, os.path.join(os.getcwd(), 'trading'))

def analyze_current_trades():
    """Analyze current trades with specific risk/reward details."""
    print("💰 TRADE RISK/REWARD ANALYSIS")
    print("=" * 50)
    
    try:
        from core.automation_control import get_automation_engine
        automation = get_automation_engine()
        
        if not automation.executed_trades:
            print("❌ No trades executed yet")
            print("\n🔍 To execute trades:")
            print("   1. Start automation (Conservative/Balanced mode)")
            print("   2. Add TTM opportunities to watchlist")
            print("   3. A+ setups will trigger automatic trades")
            return
        
        print(f"📊 Found {len(automation.executed_trades)} executed trades:\n")
        
        total_risk = 0
        total_reward = 0
        
        for i, trade in enumerate(automation.executed_trades):
            symbol = trade['symbol']
            shares = trade['shares']
            entry = trade['entry_price']
            stop = trade['stop_loss']
            target = trade['take_profit']
            
            # Calculate risk and reward
            risk_per_share = abs(entry - stop)
            reward_per_share = abs(target - entry)
            max_risk = risk_per_share * shares
            max_reward = reward_per_share * shares
            
            # Determine direction
            direction = "LONG" if target > entry else "SHORT"
            
            # Risk/Reward ratio
            rr_ratio = reward_per_share / risk_per_share if risk_per_share > 0 else 0
            
            print(f"🎯 **TRADE #{i+1}: {symbol}**")
            print(f"   📈 Direction: {direction}")
            print(f"   📊 Position: {shares} shares")
            print(f"   💵 Entry Price: ${entry:.2f}")
            print(f"   🛑 Stop Loss: ${stop:.2f}")
            print(f"   🎯 Target: ${target:.2f}")
            print(f"   ❌ **MAX RISK: ${max_risk:.2f}**")
            print(f"   ✅ **MAX REWARD: ${max_reward:.2f}**")
            print(f"   📊 Risk/Reward: 1:{rr_ratio:.1f}")
            print(f"   🔒 Alpaca Order ID: {trade.get('alpaca_order_id', 'N/A')}")
            print()
            
            total_risk += max_risk
            total_reward += max_reward
        
        print("=" * 50)
        print(f"💰 **PORTFOLIO TOTALS:**")
        print(f"   ❌ **TOTAL MAX RISK: ${total_risk:.2f}**")
        print(f"   ✅ **TOTAL MAX REWARD: ${total_reward:.2f}**")
        print(f"   📊 Overall R:R: 1:{total_reward/total_risk:.1f}" if total_risk > 0 else "   📊 Overall R:R: N/A")
        
    except Exception as e:
        print(f"❌ Error analyzing trades: {e}")

def check_stop_loss_functionality():
    """Check if stop losses are actually working."""
    print("\n🛑 STOP LOSS FUNCTIONALITY CHECK")
    print("=" * 40)
    
    try:
        from trading.alpaca_trading import get_paper_trader
        
        # Test Alpaca connection
        trader = get_paper_trader()
        account = trader.get_account_info()
        
        if account:
            print("✅ Alpaca connection working")
            print(f"   Account: {account.get('account_number', 'N/A')}")
            print(f"   Paper Trading: {trader.paper_trading}")
        
        # Check if stop loss method exists
        if hasattr(trader, '_place_bracket_orders'):
            print("✅ Stop loss functionality exists")
            print("   • Bracket orders implemented")
            print("   • Stop loss orders placed automatically")
            print("   • Take profit orders placed automatically")
        else:
            print("❌ Stop loss functionality missing")
        
        # Check recent orders for stop losses
        try:
            positions = trader.get_positions()
            print(f"\n📊 Current Positions: {len(positions)}")
            
            for pos in positions[:5]:  # Show first 5
                symbol = pos.get('symbol', 'N/A')
                qty = pos.get('qty', '0')
                side = pos.get('side', 'N/A')
                unrealized_pl = float(pos.get('unrealized_pl', 0))
                print(f"   {symbol}: {side} {qty} shares (P&L: ${unrealized_pl:.2f})")
                
        except Exception as e:
            print(f"⚠️ Could not check positions: {e}")
            
    except Exception as e:
        print(f"❌ Error checking stop loss functionality: {e}")

def analyze_specific_symbol(symbol):
    """Analyze risk/reward for a specific symbol."""
    print(f"\n🎯 ANALYZING {symbol.upper()}")
    print("=" * 30)
    
    try:
        from core.automation_control import get_automation_engine
        automation = get_automation_engine()
        
        # Find trades for this symbol
        symbol_trades = [t for t in automation.executed_trades if t['symbol'].upper() == symbol.upper()]
        
        if not symbol_trades:
            print(f"❌ No trades found for {symbol.upper()}")
            
            # Check if it's in the watchlist
            try:
                from core.live_dashboard import get_dashboard
                dashboard = get_dashboard()
                print(f"\n🔍 Check the Live Dashboard watchlist for {symbol.upper()} opportunities")
            except:
                pass
            return
        
        for trade in symbol_trades:
            shares = trade['shares']
            entry = trade['entry_price']
            stop = trade['stop_loss']
            target = trade['take_profit']
            
            risk = abs(entry - stop) * shares
            reward = abs(target - entry) * shares
            direction = "LONG" if target > entry else "SHORT"
            
            print(f"📈 **{symbol.upper()} TRADE DETAILS:**")
            print(f"   Direction: {direction}")
            print(f"   Position Size: {shares} shares")
            print(f"   Entry: ${entry:.2f}")
            print(f"   Stop Loss: ${stop:.2f}")
            print(f"   Target: ${target:.2f}")
            print(f"   **If WRONG: You lose ${risk:.2f}**")
            print(f"   **If RIGHT: You gain ${reward:.2f}**")
            print(f"   Risk/Reward: 1:{reward/risk:.1f}" if risk > 0 else "   Risk/Reward: N/A")
            
    except Exception as e:
        print(f"❌ Error analyzing {symbol}: {e}")

def main():
    """Main analysis function."""
    print("🔍 TRADE ANALYSIS TOOL")
    print("Answers specific questions about your trades")
    print("=" * 60)
    
    # Analyze all current trades
    analyze_current_trades()
    
    # Check stop loss functionality
    check_stop_loss_functionality()
    
    # Analyze PLTR specifically (since user asked about it)
    analyze_specific_symbol("PLTR")
    
    print("\n" + "=" * 60)
    print("💡 **KEY POINTS:**")
    print("   • All trades are LONG positions (buy low, sell high)")
    print("   • Stop losses are automatically placed through Alpaca")
    print("   • Risk is limited to stop loss distance × shares")
    print("   • Reward is target distance × shares")
    print("   • Paper trading is currently enabled (safe)")

if __name__ == "__main__":
    main()
