"""Test TTM GUI Scanner Display

This creates mock data to test that the GUI table is displaying results correctly.
"""

def test_ttm_gui_display():
    """Test the TTM GUI with mock data."""
    
    # Mock scanner result that matches the expected format
    mock_scanner_result = """🎯 TTM SQUEEZE OPPORTUNITIES FOUND:

🚨 HIGH GRADE OPPORTUNITIES (A/B):
📈 MSFT (1hour) - Grade A+ (100%)
   🔥 SQUEEZE RELEASE | 📈 UP | $463.28
   Entry: $463.28 | Stop: $454.01 | Target: $491.08

📈 AAPL (15min) - Grade A (95%)
   🔥 SQUEEZE RELEASE | 📈 UP | $195.50
   Entry: $195.50 | Stop: $190.44 | Target: $205.83

📈 NVDA (5min) - Grade B (85%)
   ⏳ IN SQUEEZE | 📈 UP | $875.25
   Entry: $875.25 | Stop: $849.19 | Target: $927.37

📊 OTHER OPPORTUNITIES:
📊 TSLA (15min) - Grade C | 🔥 RELEASE | $245.75
📊 GOOGL (1hour) - Grade C | ⏳ SQUEEZE | $142.30
"""
    
    # Test the parser
    from tkinter_trading_interface import TradingInterface
    
    interface = TradingInterface()
    results = interface.parse_scan_results(mock_scanner_result, 'All', 10)
    
    print(f"🧪 TTM GUI PARSER TEST RESULTS:")
    print(f"=" * 50)
    print(f"📊 Parsed {len(results)} opportunities:")
    print()
    
    for i, result in enumerate(results, 1):
        print(f"{i}. {result['symbol']} ({result['timeframe']})")
        print(f"   Grade: {result['grade']} | Confidence: {result['confidence']:.0f}%")
        print(f"   Entry: ${result['entry']:.2f}")
        print(f"   Stop: ${result['stop']:.2f}")
        print(f"   Target: ${result['target']:.2f}")
        print(f"   Risk/Reward: {result['rr']}")
        print()
    
    # Test grade filtering
    print(f"🎯 TESTING GRADE FILTERS:")
    print(f"-" * 30)
    
    for min_grade in ['A+', 'A', 'B', 'C']:
        filtered = interface.parse_scan_results(mock_scanner_result, min_grade, 10)
        print(f"Min Grade {min_grade}: {len(filtered)} results")
    
    print()
    print(f"✅ Parser test completed!")
    print(f"💡 If you see results above, the parser is working correctly.")
    print(f"🎯 The GUI should now display TTM opportunities in the table.")
    
    return results

if __name__ == "__main__":
    test_ttm_gui_display()
