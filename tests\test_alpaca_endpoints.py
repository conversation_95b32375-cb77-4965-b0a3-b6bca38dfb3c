#!/usr/bin/env python3
"""Test Alpaca API endpoints directly."""

import requests
import json

def test_alpaca_contracts_api():
    """Test the Alpaca options contracts API."""
    print("🔍 Testing Alpaca Options Contracts API")
    print("-" * 50)
    
    url = "https://paper-api.alpaca.markets/v2/options/contracts"
    
    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": "PK43FUDB28UZYZ87BT2V",
        "APCA-API-SECRET-KEY": "ICvalUW5EpIlQnInIidzIoDXrqQMdJ2CZJCD7RKg"
    }
    
    params = {
        "underlying_symbols": "AAPL",
        "status": "active",
        "limit": 10
    }
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=15)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ SUCCESS!")
            print(f"Response keys: {list(data.keys())}")
            
            if "option_contracts" in data:
                contracts = data["option_contracts"]
                print(f"Number of contracts: {len(contracts)}")
                
                if contracts:
                    contract = contracts[0]
                    print(f"\nSample contract:")
                    print(f"  Symbol: {contract['symbol']}")
                    print(f"  Strike: ${contract['strike_price']}")
                    print(f"  Type: {contract['type']}")
                    print(f"  Expiration: {contract['expiration_date']}")
                    print(f"  Close Price: ${contract.get('close_price', 'N/A')}")
                    print(f"  Open Interest: {contract.get('open_interest', 'N/A')}")
            else:
                print("❌ No option_contracts in response")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text[:300]}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_alpaca_market_data():
    """Test the Alpaca market data API."""
    print(f"\n🔍 Testing Alpaca Market Data API")
    print("-" * 50)
    
    # First get a contract symbol
    contracts_url = "https://paper-api.alpaca.markets/v2/options/contracts"
    
    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": "PK43FUDB28UZYZ87BT2V",
        "APCA-API-SECRET-KEY": "ICvalUW5EpIlQnInIidzIoDXrqQMdJ2CZJCD7RKg"
    }
    
    params = {
        "underlying_symbols": "AAPL",
        "status": "active",
        "limit": 5
    }
    
    try:
        response = requests.get(contracts_url, headers=headers, params=params, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            if "option_contracts" in data and data["option_contracts"]:
                contract_symbol = data["option_contracts"][0]["symbol"]
                print(f"Testing market data for: {contract_symbol}")
                
                # Test market data API
                market_url = f"https://data.sandbox.alpaca.markets/v1beta1/options/snapshots/{contract_symbol}"
                
                market_response = requests.get(market_url, headers=headers, timeout=15)
                print(f"Market Data Status: {market_response.status_code}")
                
                if market_response.status_code == 200:
                    market_data = market_response.json()
                    print("✅ Market Data SUCCESS!")
                    print(f"Response keys: {list(market_data.keys())}")
                    
                    if "snapshots" in market_data:
                        snapshots = market_data["snapshots"]
                        print(f"Number of snapshots: {len(snapshots)}")
                        
                        if contract_symbol in snapshots:
                            snapshot = snapshots[contract_symbol]
                            print(f"\nSnapshot data:")
                            print(f"  Keys: {list(snapshot.keys())}")
                            
                            if "latestTrade" in snapshot:
                                trade = snapshot["latestTrade"]
                                print(f"  Latest Trade: ${trade.get('p', 'N/A')}")
                            
                            if "latestQuote" in snapshot:
                                quote = snapshot["latestQuote"]
                                print(f"  Latest Quote: Bid=${quote.get('bp', 'N/A')}, Ask=${quote.get('ap', 'N/A')}")
                        else:
                            print(f"❌ No snapshot for {contract_symbol}")
                    else:
                        print("❌ No snapshots in market data response")
                else:
                    print(f"❌ Market Data Error: {market_response.status_code}")
                    print(f"Response: {market_response.text[:200]}")
            else:
                print("❌ No contracts found to test market data")
        else:
            print(f"❌ Contracts API failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_multiple_symbols():
    """Test multiple symbols for options availability."""
    print(f"\n🔍 Testing Multiple Symbols")
    print("-" * 50)
    
    symbols = ["AAPL", "TSLA", "SPY", "QQQ", "MSFT"]
    
    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": "PK43FUDB28UZYZ87BT2V",
        "APCA-API-SECRET-KEY": "ICvalUW5EpIlQnInIidzIoDXrqQMdJ2CZJCD7RKg"
    }
    
    for symbol in symbols:
        url = "https://paper-api.alpaca.markets/v2/options/contracts"
        params = {
            "underlying_symbols": symbol,
            "status": "active",
            "limit": 5
        }
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if "option_contracts" in data:
                    count = len(data["option_contracts"])
                    print(f"  {symbol}: ✅ {count} contracts")
                else:
                    print(f"  {symbol}: ⚠️ No contracts data")
            else:
                print(f"  {symbol}: ❌ Error {response.status_code}")
                
        except Exception as e:
            print(f"  {symbol}: ❌ Exception: {str(e)[:50]}")

if __name__ == "__main__":
    test_alpaca_contracts_api()
    test_alpaca_market_data()
    test_multiple_symbols()
    
    print(f"\n🎉 Alpaca API Tests Completed!")
    print("=" * 60)
