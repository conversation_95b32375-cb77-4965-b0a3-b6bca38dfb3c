#!/usr/bin/env python3
"""
Compare Existing TTM vs New Dynamic AI System
"""

def test_existing_ttm():
    """Test your existing TTM scanner."""
    print("🔍 TESTING EXISTING TTM SCANNER")
    print("=" * 50)
    
    try:
        from scanners.proper_ttm_squeeze_scanner import ProperTTMSqueezeScanner
        
        scanner = ProperTTMSqueezeScanner()
        print("Running existing TTM scanner...")
        
        opportunities = scanner.scan_all_symbols()
        
        print(f"\nExisting TTM Scanner Results: {len(opportunities)} opportunities")
        
        if opportunities:
            print("\nTop 5 Results:")
            for i, opp in enumerate(opportunities[:5]):
                symbol = opp['symbol']
                grade = opp['grade']
                confidence = opp['confidence'] * 100
                setup = opp.get('setup_description', 'N/A')
                price = opp.get('current_price', 0)
                
                print(f"{i+1}. {symbol}: Grade {grade} ({confidence:.0f}% confidence)")
                print(f"   Setup: {setup}")
                print(f"   Price: ${price:.2f}")
                print()
        else:
            print("No opportunities found with existing scanner")
            
        return opportunities
        
    except Exception as e:
        print(f"Error running existing scanner: {e}")
        return []

def test_dynamic_ai():
    """Test the new dynamic AI system."""
    print("\n🤖 TESTING DYNAMIC AI SYSTEM")
    print("=" * 50)
    
    try:
        from ai_ttm_dynamic_screener import DynamicTTMScreener
        
        screener = DynamicTTMScreener()
        if screener.model is None or screener.pattern_config is None:
            print("❌ Dynamic AI system not properly loaded")
            return []
        
        print("Running dynamic AI screener...")
        
        # Test with same symbols as existing scanner
        test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'NFLX']
        opportunities = screener.scan_symbols(test_symbols, min_composite_score=0.4)
        
        print(f"\nDynamic AI Results: {len(opportunities)} opportunities")
        
        if opportunities:
            print("\nTop 5 Results:")
            for i, opp in enumerate(opportunities[:5]):
                symbol = opp['symbol']
                ai_score = opp['ai_score']
                pattern_score = opp['pattern_score']
                composite = opp['composite_score']
                confidence = opp['confidence']
                setup = opp['setup']
                price = opp['price']
                
                print(f"{i+1}. {symbol}: Composite {composite:.1%} ({confidence})")
                print(f"   AI: {ai_score:.1%} | Pattern: {pattern_score:.1%}")
                print(f"   Setup: {setup}")
                print(f"   Price: ${price:.2f}")
                print()
        else:
            print("No opportunities found with dynamic AI system")
            
        return opportunities
        
    except Exception as e:
        print(f"Error running dynamic AI system: {e}")
        return []

def compare_systems():
    """Compare the two systems side by side."""
    print("\n📊 SYSTEM COMPARISON")
    print("=" * 70)
    
    # Test both systems
    existing_results = test_existing_ttm()
    ai_results = test_dynamic_ai()
    
    print("\n🔍 COMPARISON SUMMARY:")
    print("=" * 50)
    
    print(f"Existing TTM Scanner: {len(existing_results)} opportunities")
    print(f"Dynamic AI System:   {len(ai_results)} opportunities")
    
    if len(existing_results) == 0 and len(ai_results) == 0:
        print("\n❌ Both systems found zero opportunities")
        print("This suggests current market conditions are not favorable for TTM setups")
        
    elif len(existing_results) > 0 and len(ai_results) == 0:
        print("\n🎯 EXISTING SYSTEM WINS")
        print("- Your current TTM scanner found opportunities")
        print("- Dynamic AI system found none (too restrictive)")
        print("- AI system may be overfitted or too conservative")
        
    elif len(existing_results) == 0 and len(ai_results) > 0:
        print("\n🤖 DYNAMIC AI WINS")
        print("- AI system found opportunities your scanner missed")
        print("- AI may be identifying subtle patterns")
        print("- Need to verify if AI opportunities are actually good")
        
    else:
        print("\n⚖️ BOTH FOUND OPPORTUNITIES")
        print("- Need to compare quality and overlap")
        print("- Check if they're finding the same symbols")
        
        # Check for overlap
        existing_symbols = {opp['symbol'] for opp in existing_results}
        ai_symbols = {opp['symbol'] for opp in ai_results}
        overlap = existing_symbols & ai_symbols
        
        print(f"- Symbol overlap: {len(overlap)} symbols")
        if overlap:
            print(f"  Common symbols: {', '.join(overlap)}")

def main():
    print("🔍 TTM SYSTEM COMPARISON")
    print("=" * 70)
    print("Comparing your existing TTM scanner vs new Dynamic AI system")
    print()
    
    compare_systems()
    
    print("\n💡 NEXT STEPS:")
    print("1. If existing scanner finds more opportunities → stick with existing")
    print("2. If AI finds different opportunities → investigate quality")
    print("3. If both find nothing → market conditions unfavorable")
    print("4. Consider hybrid approach using both systems")

if __name__ == "__main__":
    main()
