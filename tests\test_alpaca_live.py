#!/usr/bin/env python3
"""Test Alpaca options API during market hours with updated credentials."""

import requests
import json
from datetime import datetime
import pytz

def check_market_hours():
    """Check if market is currently open."""
    et = pytz.timezone('US/Eastern')
    now_et = datetime.now(et)
    
    print(f"🕐 Current Time (ET): {now_et.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"📅 Day of Week: {now_et.strftime('%A')}")
    
    # Market hours: 9:30 AM - 4:00 PM ET, Monday-Friday
    if now_et.weekday() >= 5:  # Saturday = 5, Sunday = 6
        print("❌ Market CLOSED: Weekend")
        return False
    
    market_open = now_et.replace(hour=9, minute=30, second=0, microsecond=0)
    market_close = now_et.replace(hour=16, minute=0, second=0, microsecond=0)
    
    if market_open <= now_et <= market_close:
        print("✅ Market OPEN: Regular trading hours")
        return True
    else:
        print(f"❌ Market CLOSED: Outside hours (9:30 AM - 4:00 PM ET)")
        print(f"   Next open: {market_open.strftime('%A %H:%M %Z') if now_et < market_open else 'Tomorrow 9:30 AM'}")
        return False

def test_alpaca_auth():
    """Test Alpaca authentication with updated credentials."""
    print(f"\n🔍 Testing Alpaca Authentication")
    print("=" * 50)
    
    # Updated credentials
    api_key_id = "********************"
    api_secret_key = "bD6TwekPi26QDko89afY7CYNnRfkb4vHU1gnqiPm"
    
    headers = {
        "APCA-API-KEY-ID": api_key_id,
        "APCA-API-SECRET-KEY": api_secret_key
    }
    
    # Test both paper and live account endpoints
    endpoints = [
        ("Paper Account", "https://paper-api.alpaca.markets/v2/account"),
        ("Live Account", "https://api.alpaca.markets/v2/account")
    ]
    
    for name, url in endpoints:
        print(f"\n📊 Testing {name}")
        print(f"URL: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ SUCCESS! {name} authenticated")
                print(f"Account ID: {data.get('id', 'N/A')}")
                print(f"Status: {data.get('status', 'N/A')}")
                print(f"Trading Blocked: {data.get('trading_blocked', 'N/A')}")
                return True  # Found working endpoint
                
            elif response.status_code == 403:
                print(f"🚫 FORBIDDEN: {response.text[:100]}")
            elif response.status_code == 401:
                print(f"🚫 UNAUTHORIZED: {response.text[:100]}")
            else:
                print(f"❌ Error {response.status_code}: {response.text[:100]}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    return False

def test_alpaca_options():
    """Test Alpaca options endpoints with updated credentials."""
    print(f"\n🔍 Testing Alpaca Options APIs")
    print("=" * 50)
    
    api_key_id = "********************"
    api_secret_key = "bD6TwekPi26QDko89afY7CYNnRfkb4vHU1gnqiPm"
    
    headers = {
        "APCA-API-KEY-ID": api_key_id,
        "APCA-API-SECRET-KEY": api_secret_key
    }
    
    symbol = "AAPL"
    
    # Test different options endpoints
    endpoints = [
        ("Paper Options Contracts", "https://paper-api.alpaca.markets/v2/options/contracts"),
        ("Live Options Contracts", "https://api.alpaca.markets/v2/options/contracts"),
        ("Market Data Options", "https://data.alpaca.markets/v1beta1/options/snapshots"),
        ("Sandbox Options", "https://data.sandbox.alpaca.markets/v1beta1/options/snapshots")
    ]
    
    for name, base_url in endpoints:
        print(f"\n📊 Testing {name}")
        
        try:
            if "snapshots" in base_url:
                url = f"{base_url}/{symbol}"
                params = {}
            else:
                url = base_url
                params = {"underlying_symbols": symbol, "limit": 10}
            
            print(f"URL: {url}")
            print(f"Params: {params}")
            
            response = requests.get(url, headers=headers, params=params, timeout=15)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ SUCCESS! Got response")
                
                # Analyze response structure
                if isinstance(data, dict):
                    print(f"Response keys: {list(data.keys())}")
                    
                    if "option_contracts" in data:
                        contracts = data["option_contracts"]
                        print(f"Found {len(contracts)} option contracts")
                        if contracts:
                            sample = contracts[0]
                            print(f"Sample contract: {sample.get('symbol', 'N/A')}")
                            print(f"Strike: ${sample.get('strike_price', 'N/A')}")
                            print(f"Expiration: {sample.get('expiration_date', 'N/A')}")
                            return True  # Found real options data!
                    
                    elif "snapshots" in data:
                        snapshots = data["snapshots"]
                        print(f"Found {len(snapshots)} option snapshots")
                        if snapshots:
                            print(f"Sample symbols: {list(snapshots.keys())[:3]}")
                            return True  # Found real options data!
                    
                    else:
                        print(f"No options data in response")
                        
                elif isinstance(data, list):
                    print(f"Got list with {len(data)} items")
                    if data:
                        print(f"Sample item: {data[0]}")
                        return True
                
            elif response.status_code == 403:
                print(f"🚫 FORBIDDEN: {response.text[:150]}")
            elif response.status_code == 401:
                print(f"🚫 UNAUTHORIZED: {response.text[:150]}")
            else:
                print(f"❌ Error {response.status_code}: {response.text[:150]}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    return False

def main():
    """Main test function."""
    print("🔍 ALPACA OPTIONS API LIVE TEST")
    print("=" * 60)
    
    # Check market hours first
    market_open = check_market_hours()
    
    # Test authentication
    auth_success = test_alpaca_auth()
    
    if not auth_success:
        print(f"\n❌ AUTHENTICATION FAILED")
        print("Cannot test options APIs without valid authentication")
        return
    
    # Test options APIs
    options_success = test_alpaca_options()
    
    print(f"\n🎯 SUMMARY")
    print("=" * 60)
    print(f"Market Open: {'✅ YES' if market_open else '❌ NO'}")
    print(f"Authentication: {'✅ SUCCESS' if auth_success else '❌ FAILED'}")
    print(f"Options Data: {'✅ SUCCESS' if options_success else '❌ FAILED'}")
    
    if market_open and auth_success and options_success:
        print(f"\n🎉 ALL SYSTEMS GO! Real options data available")
    elif not market_open:
        print(f"\n⏰ Market closed - test again during trading hours")
    elif not auth_success:
        print(f"\n🔑 Fix authentication first")
    else:
        print(f"\n🔧 Options API access needs configuration")

if __name__ == "__main__":
    main()
