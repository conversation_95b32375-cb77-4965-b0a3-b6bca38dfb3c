#!/usr/bin/env python3
"""
Test automation push functionality
"""
import sys
import os
sys.path.insert(0, os.getcwd())

def test_automation_connection():
    """Test if automation system is accessible"""
    print("🔍 TESTING AUTOMATION CONNECTION")
    print("=" * 40)
    
    try:
        from core.automation_control import get_automation_engine
        automation = get_automation_engine()
        
        print(f"✅ Automation engine accessible")
        print(f"📊 Running status: {automation.is_running}")
        print(f"🎯 Mode: {getattr(automation, 'mode', 'Unknown')}")
        
        # Check if automation has callbacks
        from core.real_time_monitor import get_monitor
        monitor = get_monitor()
        
        callbacks = monitor.callbacks.get('new_setup', [])
        print(f"🔗 Automation callbacks: {len(callbacks)}")
        
        if len(callbacks) > 0:
            print(f"✅ Automation callbacks are registered")
            return True
        else:
            print(f"❌ No automation callbacks found")
            return False
            
    except Exception as e:
        print(f"❌ Automation connection error: {e}")
        return False

def test_a_plus_opportunity_push():
    """Test pushing A+ opportunity to automation"""
    print(f"\n🚀 TESTING A+ OPPORTUNITY PUSH")
    print("=" * 35)
    
    # Create test A+ opportunity
    test_setup = {
        'symbol': 'PLTR',
        'grade': 'A+',
        'confidence': 1.0,  # 100%
        'timeframe': '15min',
        'entry_price': 130.39,
        'stop_loss': 127.18,
        'target_price': 137.82,
        'squeeze_release': True,
        'momentum_up': True
    }
    
    print(f"📊 Test setup: {test_setup['symbol']} Grade {test_setup['grade']} ({test_setup['confidence']*100:.0f}%)")
    
    try:
        from core.live_dashboard import get_dashboard
        dashboard = get_dashboard()
        
        # Test the push function
        dashboard._push_to_automation(test_setup)
        
        print(f"✅ Push function executed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Push test error: {e}")
        return False

def test_scanner_to_automation_flow():
    """Test complete flow from scanner to automation"""
    print(f"\n🔄 TESTING COMPLETE SCANNER → AUTOMATION FLOW")
    print("=" * 50)
    
    try:
        from scanners.proper_ttm_squeeze_scanner import ProperTTMSqueezeScanner
        from core.automation_control import get_automation_engine
        
        # Start automation if not running
        automation = get_automation_engine()
        if not automation.is_running:
            print("🚀 Starting automation in conservative mode...")
            automation.start_automation("conservative")
        
        # Scan PLTR (known A+ opportunity)
        scanner = ProperTTMSqueezeScanner()
        result = scanner.scan_symbol('PLTR', '15min')
        
        if result and result['grade'] == 'A+':
            print(f"✅ Found A+ opportunity: PLTR Grade {result['grade']} ({result['confidence']*100:.0f}%)")
            
            # Test dashboard push
            from core.live_dashboard import get_dashboard
            dashboard = get_dashboard()
            dashboard._push_to_automation(result)
            
            print(f"✅ Complete flow test successful")
            return True
        else:
            print(f"❌ No A+ opportunity found for PLTR")
            return False
            
    except Exception as e:
        print(f"❌ Flow test error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 AUTOMATION PUSH TEST SUITE")
    print("=" * 40)
    
    # Test 1: Automation connection
    connection_ok = test_automation_connection()
    
    # Test 2: A+ opportunity push
    push_ok = test_a_plus_opportunity_push()
    
    # Test 3: Complete flow
    flow_ok = test_scanner_to_automation_flow()
    
    print(f"\n🎯 TEST RESULTS:")
    print(f"   🔗 Automation connection: {'✅ PASS' if connection_ok else '❌ FAIL'}")
    print(f"   🚀 A+ opportunity push: {'✅ PASS' if push_ok else '❌ FAIL'}")
    print(f"   🔄 Complete flow: {'✅ PASS' if flow_ok else '❌ FAIL'}")
    
    if connection_ok and push_ok and flow_ok:
        print(f"\n✅ ALL TESTS PASSED - Scanner will push A+ opportunities to auto trader!")
        print(f"💡 Click 'SCAN S&P 500' and watch for automation alerts")
    else:
        print(f"\n❌ SOME TESTS FAILED - Check automation setup")

if __name__ == "__main__":
    main()
