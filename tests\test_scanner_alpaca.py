#!/usr/bin/env python3
"""Test the options scanner with Alpaca integration."""

from options_opportunity_scanner import OptionsOpportunityScanner

def test_scanner():
    """Test the options scanner with Alpaca API."""
    print("🧪 Testing Options Scanner with Alpaca API")
    print("=" * 50)
    
    # Initialize scanner
    scanner = OptionsOpportunityScanner()
    
    # Test single stock
    test_symbol = "AAPL"
    print(f"\n📊 Testing {test_symbol}...")
    
    # Test stock price
    print(f"1. Getting stock price for {test_symbol}...")
    stock_price = scanner._get_stock_price(test_symbol)
    print(f"   Stock Price: ${stock_price:.2f}")
    
    # Test Alpaca options data
    print(f"2. Getting Alpaca options data for {test_symbol}...")
    alpaca_data = scanner._get_alpaca_options_data(test_symbol)
    print(f"   Alpaca Options Found: {len(alpaca_data)}")
    
    if alpaca_data:
        print(f"   Sample options:")
        for i, option in enumerate(alpaca_data[:3]):
            print(f"     {i+1}. {option['symbol']} - Strike: ${option['strike']:.2f}, Type: {option['type']}, Price: ${option['lastPrice']:.2f}")
    
    # Test full options chain
    print(f"3. Getting full options chain for {test_symbol}...")
    options_chain = scanner._get_options_chain(test_symbol)
    print(f"   Total Options Found: {len(options_chain)}")
    
    if options_chain:
        print(f"   Sample contracts:")
        for i, contract in enumerate(options_chain[:3]):
            print(f"     {i+1}. {contract['symbol']} - Strike: ${contract['strike']:.2f}, Type: {contract['type']}, Price: ${contract['lastPrice']:.2f}")
    
    print(f"\n✅ Test completed!")

if __name__ == "__main__":
    test_scanner()
