#!/usr/bin/env python3
"""
Test dashboard data format
"""
import sys
import os
sys.path.insert(0, os.getcwd())

def test_direct_data_injection():
    """Test injecting data directly into dashboard"""
    print("🔍 TESTING DIRECT DATA INJECTION INTO DASHBOARD")
    print("=" * 50)
    
    # Create test data in the format the dashboard expects
    test_opportunities = [
        {
            'symbol': 'PLTR',
            'timeframe': '15min',
            'grade': 'A+',
            'confidence': 100,
            'entry_price': 129.05,
            'stop_loss': 125.64,
            'target_price': 136.79,
            'squeeze_release': True,
            'momentum_up': True
        },
        {
            'symbol': 'TSLA',
            'timeframe': '15min', 
            'grade': 'A+',
            'confidence': 100,
            'entry_price': 332.18,
            'stop_loss': 323.34,
            'target_price': 351.91,
            'squeeze_release': False,
            'momentum_up': True
        },
        {
            'symbol': 'AAPL',
            'timeframe': '15min',
            'grade': 'B',
            'confidence': 95,
            'entry_price': 202.87,
            'stop_loss': 198.81,
            'target_price': 215.04,
            'squeeze_release': False,
            'momentum_up': True
        },
        {
            'symbol': 'MSFT',
            'timeframe': '15min',
            'grade': 'B', 
            'confidence': 95,
            'entry_price': 464.04,
            'stop_loss': 453.56,
            'target_price': 491.48,
            'squeeze_release': False,
            'momentum_up': True
        },
        {
            'symbol': 'NVDA',
            'timeframe': '15min',
            'grade': 'B',
            'confidence': 95,
            'entry_price': 140.62,
            'stop_loss': 137.41,
            'target_price': 148.86,
            'squeeze_release': False,
            'momentum_up': True
        }
    ]
    
    print(f"✅ Created {len(test_opportunities)} test opportunities")
    for opp in test_opportunities:
        print(f"   {opp['symbol']}: Grade {opp['grade']} ({opp['confidence']}%) - ${opp['entry_price']:.2f}")
    
    return test_opportunities

def inject_into_dashboard(opportunities):
    """Inject opportunities directly into dashboard"""
    print(f"\n🚀 INJECTING {len(opportunities)} OPPORTUNITIES INTO DASHBOARD")
    print("=" * 55)
    
    try:
        from core.live_dashboard import get_dashboard
        dashboard = get_dashboard()
        
        if not hasattr(dashboard, 'positions_tree') or not dashboard.positions_tree:
            print("❌ Dashboard not initialized or no positions tree")
            return False
        
        # Clear existing items
        for item in dashboard.positions_tree.get_children():
            dashboard.positions_tree.delete(item)
        
        # Add test opportunities
        added_count = 0
        for setup in opportunities:
            try:
                # Calculate risk/reward
                risk_per_share = abs(setup['entry_price'] - setup['stop_loss'])
                reward_per_share = abs(setup['target_price'] - setup['entry_price'])
                rr_ratio = f"1:{reward_per_share/risk_per_share:.1f}" if risk_per_share > 0 else "1:0"
                
                # Calculate position size and dollar amounts
                account_size = 10000
                risk_amount = account_size * 0.01  # 1% risk
                shares = int(risk_amount / risk_per_share) if risk_per_share > 0 else 100
                shares = max(1, min(shares, 1000))
                
                dollar_risk = risk_per_share * shares
                dollar_reward = reward_per_share * shares
                
                # Determine status
                status = "🔥 SQUEEZE RELEASE" if setup.get('squeeze_release', False) else "⏳ IN SQUEEZE"
                
                values = (
                    setup['symbol'],
                    setup['timeframe'],
                    setup['grade'],
                    f"{setup['confidence']:.0f}%",
                    status,
                    f"${setup['entry_price']:.2f}",
                    f"${setup['stop_loss']:.2f}",
                    f"${setup['target_price']:.2f}",
                    rr_ratio,
                    "LONG",
                    f"${dollar_risk:.0f}",
                    f"${dollar_reward:.0f}"
                )
                
                dashboard.positions_tree.insert('', 'end', values=values)
                added_count += 1
                
            except Exception as e:
                print(f"❌ Error adding {setup['symbol']}: {e}")
        
        print(f"✅ Successfully added {added_count} opportunities to dashboard")
        
        # Update status
        if hasattr(dashboard, '_update_status'):
            dashboard._update_status()
        
        return True
        
    except Exception as e:
        print(f"❌ Dashboard injection error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 DASHBOARD DATA FORMAT TEST")
    print("=" * 40)
    
    # Create test data
    opportunities = test_direct_data_injection()
    
    # Inject into dashboard
    success = inject_into_dashboard(opportunities)
    
    if success:
        print(f"\n✅ SUCCESS! Dashboard should now show {len(opportunities)} opportunities")
        print("💡 Check your live dashboard - it should display:")
        print("   • PLTR: A+ (100%)")
        print("   • TSLA: A+ (100%)")  
        print("   • AAPL: B (95%)")
        print("   • MSFT: B (95%)")
        print("   • NVDA: B (95%)")
    else:
        print("\n❌ FAILED to inject data into dashboard")
        print("💡 Dashboard may not be running or accessible")

if __name__ == "__main__":
    main()
