"""Demo Live TTM Trading System

This demonstrates the complete live trading workflow:
1. Find the most promising TTM squeeze setup
2. Execute the trade with proper position sizing
3. Implement trailing stops
4. Monitor the position in real-time
5. Show complete trade management
"""
from __future__ import annotations

import time
from datetime import datetime
from live_ttm_trading_system import LiveTTMTradingSystem

def demo_complete_ttm_trading():
    """Demonstrate complete TTM trading workflow."""
    print("🚀 DEMO: COMPLETE TTM SQUEEZE TRADING SYSTEM")
    print("=" * 60)
    
    # Initialize trading system
    trading_system = LiveTTMTradingSystem()
    
    print(f"📊 TRADING SYSTEM CONFIGURATION:")
    print(f"   Account Value: $10,000 (simulated)")
    print(f"   Risk Per Trade: {trading_system.risk_per_trade * 100}%")
    print(f"   Max Position Size: ${trading_system.max_position_size}")
    print(f"   Trailing Stop: {trading_system.trailing_stop_pct * 100}%")
    print(f"   Mode: {'PAPER TRADING' if trading_system.paper_trading else 'LIVE TRADING'}")
    
    print(f"\n🎯 STEP 1: SCANNING FOR BEST TTM SQUEEZE SETUP")
    print("-" * 50)
    
    # Find best TTM setup
    best_setup = trading_system.find_best_ttm_setup()
    
    if not best_setup:
        print("❌ No qualifying TTM squeeze setups found")
        print("   Try again later when market conditions improve")
        return None
    
    # Display setup details
    symbol = best_setup['symbol']
    price = best_setup['price']
    score = trading_system._score_ttm_setup(best_setup)
    
    print(f"✅ BEST TTM SETUP FOUND:")
    print(f"   Symbol: {symbol}")
    print(f"   Current Price: ${price:.2f}")
    print(f"   Setup Score: {score}/100")
    print(f"   Breakout Score: {best_setup.get('breakout_score', 'N/A')}")
    print(f"   Timeframe: {best_setup.get('timeframe', 'N/A')}")
    print(f"   Squeeze Duration: {best_setup.get('squeeze_duration', 'N/A')} periods")
    print(f"   Volume Ratio: {best_setup.get('volume_ratio', 'N/A'):.1f}x")
    print(f"   Momentum: {best_setup.get('momentum_direction', 'N/A')}")
    
    print(f"\n🎯 STEP 2: CALCULATING POSITION SIZE & RISK")
    print("-" * 50)
    
    # Calculate trade parameters
    position_size = trading_system._calculate_position_size(price)
    shares = int(position_size / price)
    actual_position_value = shares * price
    stop_loss = price * (1 - trading_system.trailing_stop_pct)
    take_profit = price + (2 * (price - stop_loss))  # 2:1 risk/reward
    
    risk_per_share = price - stop_loss
    total_risk = shares * risk_per_share
    potential_profit = shares * (take_profit - price)
    
    print(f"📊 POSITION SIZING:")
    print(f"   Target Position Size: ${position_size:.2f}")
    print(f"   Shares to Buy: {shares}")
    print(f"   Actual Position Value: ${actual_position_value:.2f}")
    print(f"   Capital Utilization: {(actual_position_value / 10000) * 100:.1f}%")
    
    print(f"\n📊 RISK MANAGEMENT:")
    print(f"   Entry Price: ${price:.2f}")
    print(f"   Stop Loss: ${stop_loss:.2f} ({trading_system.trailing_stop_pct * 100:.1f}% below entry)")
    print(f"   Take Profit: ${take_profit:.2f} (2:1 risk/reward)")
    print(f"   Risk Per Share: ${risk_per_share:.2f}")
    print(f"   Total Risk: ${total_risk:.2f}")
    print(f"   Potential Profit: ${potential_profit:.2f}")
    print(f"   Risk/Reward Ratio: 1:{potential_profit/total_risk:.1f}")
    
    print(f"\n🎯 STEP 3: EXECUTING THE TRADE")
    print("-" * 50)
    
    # Execute the trade
    success = trading_system.execute_ttm_trade(best_setup)
    
    if not success:
        print("❌ Trade execution failed")
        return None
    
    print(f"✅ TRADE EXECUTED SUCCESSFULLY!")
    print(f"   Order Type: Market Buy")
    print(f"   Execution: IMMEDIATE (paper trading)")
    print(f"   Position Added to Monitoring System")
    
    print(f"\n🎯 STEP 4: POSITION MONITORING & TRAILING STOPS")
    print("-" * 50)
    
    # Show position monitoring
    position = trading_system.active_positions[symbol]
    
    print(f"📊 ACTIVE POSITION:")
    print(f"   Symbol: {symbol}")
    print(f"   Shares: {position['quantity']}")
    print(f"   Entry Price: ${position['entry_price']:.2f}")
    print(f"   Current Stop: ${position['stop_loss']:.2f}")
    print(f"   Take Profit: ${position['take_profit']:.2f}")
    print(f"   Trailing Stop: {position['trailing_stop_pct'] * 100}%")
    
    print(f"\n🔄 SIMULATING PRICE MOVEMENT & TRAILING STOPS:")
    
    # Simulate price movements and trailing stop updates
    simulate_price_movements(trading_system, symbol, price)
    
    print(f"\n🎯 STEP 5: TRADE MANAGEMENT SUMMARY")
    print("-" * 50)
    
    if symbol in trading_system.active_positions:
        final_position = trading_system.active_positions[symbol]
        current_stop = final_position['stop_loss']
        print(f"📊 FINAL POSITION STATUS:")
        print(f"   Position: STILL ACTIVE")
        print(f"   Updated Stop Loss: ${current_stop:.2f}")
        print(f"   Stop Moved Up: ${current_stop - stop_loss:.2f}")
        print(f"   Profit Protected: ${(current_stop - position['entry_price']) * shares:.2f}")
    else:
        print(f"📊 POSITION CLOSED")
        print(f"   Check logs above for exit details")
    
    print(f"\n🎯 SYSTEM CAPABILITIES DEMONSTRATED:")
    print("✅ Automated TTM squeeze detection")
    print("✅ Intelligent setup scoring and ranking")
    print("✅ Risk-based position sizing")
    print("✅ Automatic trade execution")
    print("✅ Dynamic trailing stop management")
    print("✅ Real-time position monitoring")
    print("✅ Profit protection and risk management")
    
    return trading_system

def simulate_price_movements(trading_system, symbol, start_price):
    """Simulate price movements to demonstrate trailing stops."""
    
    # Simulate price going up (profitable scenario)
    price_moves = [
        (start_price * 1.01, "Price moves up 1%"),
        (start_price * 1.02, "Price moves up 2%"),
        (start_price * 1.035, "Price moves up 3.5%"),
        (start_price * 1.04, "Price moves up 4%"),
        (start_price * 1.045, "Price moves up 4.5%"),
    ]
    
    for new_price, description in price_moves:
        print(f"\n📈 {description} → ${new_price:.2f}")
        
        # Manually update the price and check trailing stop
        position = trading_system.active_positions[symbol]
        old_stop = position['stop_loss']
        
        # Calculate new trailing stop
        new_stop = new_price * (1 - trading_system.trailing_stop_pct)
        
        if new_stop > old_stop:
            position['stop_loss'] = new_stop
            profit_protected = (new_stop - position['entry_price']) * position['quantity']
            print(f"   🛡️ Trailing stop updated: ${old_stop:.2f} → ${new_stop:.2f}")
            print(f"   💰 Profit protected: ${profit_protected:.2f}")
        else:
            print(f"   ⏸️ Stop remains at: ${old_stop:.2f} (no update needed)")
        
        time.sleep(1)  # Pause for effect

def show_trading_system_status(trading_system):
    """Show current trading system status."""
    status = trading_system.get_trading_status()
    
    print(f"\n📊 TRADING SYSTEM STATUS:")
    print(f"   Active Positions: {status['active_positions']}")
    print(f"   Symbols: {', '.join(status['positions']) if status['positions'] else 'None'}")
    print(f"   Paper Trading: {status['paper_trading']}")
    print(f"   Monitoring: {status['monitoring']}")
    print(f"   Last Scan: {status['last_scan']}")

if __name__ == "__main__":
    print("🎯 STARTING COMPLETE TTM TRADING DEMONSTRATION")
    print("This will show the entire workflow from setup detection to trade management")
    print("=" * 70)
    
    # Run the complete demo
    trading_system = demo_complete_ttm_trading()
    
    if trading_system:
        # Show final status
        show_trading_system_status(trading_system)
        
        print(f"\n🚀 NEXT STEPS FOR LIVE TRADING:")
        print("1. Add broker API integration (Alpaca, IBKR, etc.)")
        print("2. Set up real account credentials")
        print("3. Test with paper trading extensively")
        print("4. Start with small position sizes")
        print("5. Monitor performance and adjust parameters")
        
        print(f"\n💡 TO START AUTOMATED TRADING:")
        print("   trading_system.start_automated_trading()")
        print("   (This will run continuously, finding and trading setups)")
        
        print(f"\n⚠️ IMPORTANT REMINDERS:")
        print("• This demo uses PAPER TRADING only")
        print("• Real trading requires broker API setup")
        print("• Always test thoroughly before going live")
        print("• Start with small amounts and scale up")
        print("• Monitor positions actively")
        
    else:
        print(f"\n⏳ No TTM setups available right now")
        print("Market conditions may not be optimal for squeeze setups")
        print("Try running again during active trading hours")
