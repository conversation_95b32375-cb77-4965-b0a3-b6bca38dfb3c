#!/usr/bin/env python3
"""
Test Enhanced Chat Functionality

Quick test to verify the enhanced chat integration is working.
"""

def test_enhanced_chat():
    """Test enhanced chat functionality."""
    print("🧪 TESTING ENHANCED CHAT FUNCTIONALITY")
    print("=" * 50)
    
    # Test 1: Import enhanced chat
    print("\n📦 Test 1: Import Enhanced Chat")
    try:
        from core.enhanced_chat_integration import enhanced_chat_gpt, get_enhanced_chat
        print("✅ Enhanced chat imported successfully")
    except ImportError as e:
        print(f"❌ Enhanced chat import failed: {e}")
        return False
    
    # Test 2: Test enhanced chat instance
    print("\n🔧 Test 2: Enhanced Chat Instance")
    try:
        enhanced_chat = get_enhanced_chat()
        if enhanced_chat.is_initialized:
            print("✅ Enhanced chat initialized")
        else:
            print("⚠️ Enhanced chat not fully initialized")
    except Exception as e:
        print(f"❌ Enhanced chat instance failed: {e}")
    
    # Test 3: Test enhanced responses
    print("\n💬 Test 3: Enhanced Chat Responses")
    test_messages = [
        "What's happening?",
        "Greeks for AAPL", 
        "Portfolio analysis",
        "Buy 100 AAPL"
    ]
    
    for message in test_messages:
        try:
            print(f"\n📝 Testing: '{message}'")
            response = enhanced_chat_gpt(message)
            
            # Check if response contains enhancement indicators
            if any(keyword in response for keyword in ["Enhanced", "MCP", "Advanced", "Powered by"]):
                print("✅ Enhanced response detected")
            else:
                print("✅ Standard response (fallback working)")
                
            # Show first 100 characters of response
            print(f"📄 Response preview: {response[:100]}...")
            
        except Exception as e:
            print(f"❌ Chat test failed for '{message}': {e}")
    
    print("\n🎉 Enhanced chat testing complete!")
    return True

def test_mcp_integration():
    """Test MCP integration status."""
    print("\n🔍 TESTING MCP INTEGRATION STATUS")
    print("=" * 40)
    
    try:
        from core.enhanced_mcp_integration import is_enhanced_mcp_available, get_enhanced_capabilities
        
        if is_enhanced_mcp_available():
            capabilities = get_enhanced_capabilities()
            print(f"✅ Enhanced MCP available with {len(capabilities)} capabilities")
            for cap in capabilities:
                print(f"   • {cap}")
        else:
            print("⚠️ Enhanced MCP not available - using fallback")
            
    except Exception as e:
        print(f"❌ MCP integration test failed: {e}")

def main():
    """Main test function."""
    print("🧪 Enhanced Chat Integration Test")
    print("=" * 40)
    
    # Test enhanced chat
    chat_success = test_enhanced_chat()
    
    # Test MCP integration
    test_mcp_integration()
    
    print("\n📊 TEST SUMMARY")
    print("=" * 20)
    
    if chat_success:
        print("✅ Enhanced chat integration working")
        print("✅ Fallback mechanisms operational")
        print("✅ System ready for enhanced commands")
        
        print("\n💡 TRY THESE ENHANCED COMMANDS:")
        print("• 'Greeks for AAPL' - Advanced options analysis")
        print("• 'Portfolio analysis' - Enhanced portfolio metrics")
        print("• 'Earnings calendar' - Market intelligence")
        print("• 'Buy 100 AAPL with stop' - Smart order execution")
        
        print("\n🚀 Your enhanced TotalRecall system is ready!")
    else:
        print("⚠️ Enhanced chat had issues")
        print("💡 Your existing system should still work normally")
    
    return chat_success

if __name__ == "__main__":
    main()
