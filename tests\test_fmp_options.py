#!/usr/bin/env python3
"""Test FMP options API directly to see what's happening."""

import requests
from config import get_api_key

def test_fmp_options_api():
    """Test FMP options API endpoints directly."""
    
    api_key = get_api_key("FMP_API_KEY")
    print(f"🔑 Using FMP API Key: {api_key[:8]}...")
    
    symbol = "AAPL"
    
    # Test different FMP endpoints
    endpoints = [
        f"https://financialmodelingprep.com/api/v3/options-chain/{symbol}",
        f"https://financialmodelingprep.com/api/v4/options-chain/{symbol}",
        f"https://financialmodelingprep.com/api/v3/options/{symbol}",
        f"https://financialmodelingprep.com/api/v3/available-options/{symbol}",
        f"https://financialmodelingprep.com/api/v4/options/{symbol}"
    ]
    
    for i, url in enumerate(endpoints, 1):
        print(f"\n{i}. Testing: {url}")
        print("-" * 60)
        
        try:
            params = {"apikey": api_key}
            response = requests.get(url, params=params, timeout=15)
            
            print(f"   Status Code: {response.status_code}")
            print(f"   Response Length: {len(response.text)} chars")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if isinstance(data, list):
                        print(f"   ✅ Got list with {len(data)} items")
                        if data:
                            print(f"   Sample item: {data[0]}")
                    elif isinstance(data, dict):
                        print(f"   ✅ Got dict with keys: {list(data.keys())}")
                        if "options" in data:
                            print(f"   Options found: {len(data['options'])}")
                    else:
                        print(f"   ⚠️ Unexpected data type: {type(data)}")
                except Exception as json_exc:
                    print(f"   ❌ JSON parse error: {json_exc}")
                    print(f"   Raw response: {response.text[:200]}...")
            else:
                print(f"   ❌ Error response: {response.text[:200]}...")
                
        except Exception as exc:
            print(f"   ❌ Request failed: {exc}")
    
    # Test stock price endpoint (should work)
    print(f"\n6. Testing stock price endpoint...")
    print("-" * 60)
    try:
        url = f"https://financialmodelingprep.com/api/v3/quote/{symbol}"
        params = {"apikey": api_key}
        response = requests.get(url, params=params, timeout=10)
        
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data and len(data) > 0:
                price = data[0].get("price", 0)
                print(f"   ✅ Stock price: ${price}")
            else:
                print(f"   ⚠️ No price data: {data}")
        else:
            print(f"   ❌ Error: {response.text[:200]}")
            
    except Exception as exc:
        print(f"   ❌ Stock price failed: {exc}")

if __name__ == "__main__":
    test_fmp_options_api()
