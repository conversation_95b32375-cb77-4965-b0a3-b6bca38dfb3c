#!/usr/bin/env python3
"""
Comprehensive Alpaca API diagnostics to identify exact issues.
"""

import requests
import os
from dotenv import load_dotenv

# Load environment variables - override existing ones
load_dotenv("config.env", override=True)

def test_alpaca_endpoints():
    """Test different Alpaca endpoints to identify the exact issue."""

    # Force the correct API credentials from config.env
    api_key = "PK43FUDB28UZYZ87BT2V"  # From user's memory
    secret_key = os.getenv("ALPACA_API_SECRET")

    print("🔍 Alpaca API Comprehensive Diagnostics")
    print("=" * 60)
    print(f"API Key: {api_key[:8]}...{api_key[-4:]}")
    print(f"Secret: {secret_key[:8]}...{secret_key[-4:]}")

    headers = {
        "accept": "application/json",
        "APCA-API-KEY-ID": api_key,
        "APCA-API-SECRET-KEY": secret_key
    }

    # Test different endpoint combinations
    test_configs = [
        {
            "name": "Paper Trading Account",
            "url": "https://paper-api.alpaca.markets/v2/account",
            "description": "Paper trading account endpoint"
        },
        {
            "name": "Live Trading Account",
            "url": "https://api.alpaca.markets/v2/account",
            "description": "Live trading account endpoint"
        },
        {
            "name": "Paper Assets",
            "url": "https://paper-api.alpaca.markets/v2/assets",
            "description": "Paper trading assets",
            "params": {"status": "active", "limit": 5}
        },
        {
            "name": "Live Assets",
            "url": "https://api.alpaca.markets/v2/assets",
            "description": "Live trading assets",
            "params": {"status": "active", "limit": 5}
        },
        {
            "name": "Market Data - Stock Quotes",
            "url": "https://data.alpaca.markets/v2/stocks/quotes/latest",
            "description": "Latest stock quotes",
            "params": {"symbols": "AAPL"}
        },
        {
            "name": "Market Data - IEX Stock Quotes",
            "url": "https://data.alpaca.markets/v2/stocks/quotes/latest",
            "description": "IEX stock quotes (free tier)",
            "params": {"symbols": "AAPL", "feed": "iex"}
        }
    ]

    results = []

    for config in test_configs:
        print(f"\n🧪 Testing: {config['name']}")
        print(f"   URL: {config['url']}")
        print(f"   Description: {config['description']}")

        try:
            response = requests.get(
                config["url"],
                headers=headers,
                params=config.get("params", {}),
                timeout=10
            )

            print(f"   Status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ SUCCESS!")

                # Show relevant info based on endpoint
                if "account" in config["url"]:
                    print(f"   Account ID: {data.get('id', 'N/A')}")
                    print(f"   Status: {data.get('status', 'N/A')}")
                    print(f"   Trading Blocked: {data.get('trading_blocked', 'N/A')}")
                    print(f"   Options Approved: {data.get('options_approved', 'N/A')}")
                    print(f"   Options Level: {data.get('options_trading_level', 'N/A')}")
                elif "assets" in config["url"]:
                    print(f"   Assets found: {len(data) if isinstance(data, list) else 'N/A'}")
                elif "quotes" in config["url"]:
                    print(f"   Quotes data: {bool(data.get('quotes', {}))}")

                results.append({"name": config["name"], "status": "SUCCESS", "code": 200})

            elif response.status_code == 403:
                print(f"   ❌ 403 FORBIDDEN")
                print(f"   Response: {response.text[:200]}")
                results.append({"name": config["name"], "status": "FORBIDDEN", "code": 403})

            elif response.status_code == 401:
                print(f"   ❌ 401 UNAUTHORIZED")
                print(f"   Response: {response.text[:200]}")
                results.append({"name": config["name"], "status": "UNAUTHORIZED", "code": 401})

            else:
                print(f"   ❌ HTTP {response.status_code}")
                print(f"   Response: {response.text[:200]}")
                results.append({"name": config["name"], "status": f"HTTP_{response.status_code}", "code": response.status_code})

        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            results.append({"name": config["name"], "status": "ERROR", "error": str(e)})

    # Summary and recommendations
    print(f"\n📊 SUMMARY")
    print("=" * 60)

    successful = [r for r in results if r["status"] == "SUCCESS"]
    forbidden = [r for r in results if r["status"] == "FORBIDDEN"]
    unauthorized = [r for r in results if r["status"] == "UNAUTHORIZED"]

    print(f"✅ Successful endpoints: {len(successful)}")
    for r in successful:
        print(f"   - {r['name']}")

    print(f"\n❌ Forbidden (403) endpoints: {len(forbidden)}")
    for r in forbidden:
        print(f"   - {r['name']}")

    print(f"\n🔒 Unauthorized (401) endpoints: {len(unauthorized)}")
    for r in unauthorized:
        print(f"   - {r['name']}")

    # Provide specific recommendations
    print(f"\n💡 RECOMMENDATIONS")
    print("=" * 60)

    if len(successful) == 0:
        print("🚨 CRITICAL: No endpoints are working!")
        print("   1. ❗ Check your API credentials are correct")
        print("   2. ❗ Verify the API key is active in your Alpaca dashboard")
        print("   3. ❗ Ensure you're using the right account type (paper vs live)")

    elif any("Paper Trading Account" in r["name"] for r in successful):
        print("✅ Paper trading account access works!")
        print("   1. ✅ Your credentials are valid for paper trading")
        print("   2. ✅ You can proceed with paper trading features")
        print("   3. 📝 For options, check if options trading is enabled in dashboard")

    elif any("Live Trading Account" in r["name"] for r in successful):
        print("✅ Live trading account access works!")
        print("   1. ✅ Your credentials are valid for live trading")
        print("   2. ⚠️  Be careful - this is real money trading")
        print("   3. 📝 For options, verify options trading approval level")

    if any("Market Data" in r["name"] for r in successful):
        print("✅ Market data access works!")
        print("   1. ✅ You can access stock market data")
        print("   2. 📊 Options data may require additional subscription")

    # Account type recommendation
    if any("Paper" in r["name"] for r in successful) and not any("Live" in r["name"] for r in successful):
        print("\n🎯 RECOMMENDED CONFIGURATION:")
        print("   - Use Paper Trading for development and testing")
        print("   - Paper trading has options enabled by default")
        print("   - Switch to live account only when ready for real trading")

    print(f"\n📚 NEXT STEPS:")
    print("   1. 🌐 Visit https://app.alpaca.markets/paper/dashboard")
    print("   2. 🔧 Check Account > Settings > API Keys")
    print("   3. 📊 Verify Account > Trading > Options Trading")
    print("   4. 📞 Contact Alpaca support if issues persist")

if __name__ == "__main__":
    test_alpaca_endpoints()
