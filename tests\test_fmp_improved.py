#!/usr/bin/env python3
"""Test the improved FMP integration with rate limiting and error handling."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from options_opportunity_scanner import OptionsOpportunityScanner
import requests
import time

def test_fmp_direct():
    """Test FMP API directly to check rate limits."""
    print("🔍 Testing FMP API Directly")
    print("=" * 50)
    
    api_key = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
    symbol = "AAPL"
    
    # Test different endpoints
    endpoints = [
        f"https://financialmodelingprep.com/api/v3/quote/{symbol}",
        f"https://financialmodelingprep.com/api/v3/options-chain/{symbol}",
        f"https://financialmodelingprep.com/api/v4/options-chain/{symbol}",
        f"https://financialmodelingprep.com/api/v3/options/{symbol}"
    ]
    
    for i, url in enumerate(endpoints):
        print(f"\n{i+1}. Testing: {url.split('/')[-2:]}")
        
        try:
            params = {"apikey": api_key}
            response = requests.get(url, params=params, timeout=10)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    print(f"   ✅ SUCCESS: Got {len(data)} items")
                elif isinstance(data, dict):
                    print(f"   ✅ SUCCESS: Got dict with keys: {list(data.keys())}")
                else:
                    print(f"   ⚠️ Unexpected data type: {type(data)}")
            elif response.status_code == 429:
                print(f"   🚫 RATE LIMITED")
                print(f"   Response: {response.text[:100]}")
            elif response.status_code == 403:
                print(f"   🚫 FORBIDDEN")
                print(f"   Response: {response.text[:100]}")
            else:
                print(f"   ❌ Error: {response.status_code}")
                print(f"   Response: {response.text[:100]}")
                
            # Add delay between requests
            time.sleep(0.3)
            
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def test_improved_scanner():
    """Test the improved options scanner."""
    print(f"\n🔍 Testing Improved Options Scanner")
    print("=" * 50)
    
    try:
        scanner = OptionsOpportunityScanner()
        
        # Test single stock
        symbol = "AAPL"
        print(f"\n📊 Testing {symbol}...")
        
        # Test stock price
        stock_price = scanner._get_stock_price(symbol)
        print(f"1. Stock Price: ${stock_price:.2f}")
        
        # Test FMP options data with improved handling
        print(f"2. Getting FMP options data...")
        fmp_data = scanner._get_fmp_options_data(symbol)
        print(f"   ✅ FMP returned {len(fmp_data)} options")
        
        if fmp_data:
            print(f"   📊 Sample options:")
            for i, option in enumerate(fmp_data[:3]):
                print(f"     {i+1}. {option['symbol']}")
                print(f"        Strike: ${option['strike']:.2f}, Type: {option['type']}")
                print(f"        Price: ${option['lastPrice']:.2f}")
                print(f"        IV: {option['impliedVolatility']:.3f}")
        
        # Test full options chain
        print(f"\n3. Testing full options chain...")
        options_chain = scanner._get_options_chain(symbol)
        print(f"   ✅ Options chain returned {len(options_chain)} options")
        
        # Test validation
        if options_chain:
            print(f"\n4. Testing validation...")
            valid_count = 0
            invalid_count = 0
            
            for option in options_chain:
                if scanner._validate_option_data(option):
                    valid_count += 1
                else:
                    invalid_count += 1
            
            print(f"   ✅ Valid: {valid_count}")
            print(f"   ❌ Invalid: {invalid_count}")
            print(f"   📊 Validation rate: {valid_count/(valid_count+invalid_count)*100:.1f}%")
        
        print(f"\n✅ Scanner test completed!")
        
    except Exception as e:
        print(f"❌ Scanner test failed: {e}")

def test_rate_limiting():
    """Test rate limiting behavior."""
    print(f"\n🔍 Testing Rate Limiting")
    print("=" * 50)
    
    api_key = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
    
    # Make multiple rapid requests to test rate limiting
    symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA"]
    
    for symbol in symbols:
        url = f"https://financialmodelingprep.com/api/v3/quote/{symbol}"
        params = {"apikey": api_key}
        
        try:
            start_time = time.time()
            response = requests.get(url, params=params, timeout=5)
            end_time = time.time()
            
            print(f"{symbol}: {response.status_code} ({end_time-start_time:.2f}s)")
            
            if response.status_code == 429:
                print(f"   🚫 Rate limited - waiting...")
                time.sleep(1)
            else:
                time.sleep(0.1)  # Small delay
                
        except Exception as e:
            print(f"{symbol}: ❌ {e}")

if __name__ == "__main__":
    test_fmp_direct()
    test_improved_scanner()
    test_rate_limiting()
    
    print(f"\n🎉 FMP Improved Integration Tests Completed!")
    print("=" * 60)
