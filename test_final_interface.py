#!/usr/bin/env python3
"""
Final Interface Test
Test all the button functions that were causing errors
"""
import sys
import os

# Add paths
sys.path.insert(0, os.getcwd())
sys.path.insert(0, os.path.join(os.getcwd(), 'core'))
sys.path.insert(0, os.path.join(os.getcwd(), 'trading'))

def test_monitoring_functions():
    """Test monitoring functions"""
    print("🔴 Testing Monitoring Functions")
    print("-" * 35)
    
    try:
        from core.real_time_monitor import get_monitor
        
        monitor = get_monitor()
        
        # Test start monitoring
        result = monitor.start_monitoring()
        print(f"✅ Start monitoring: {result}")
        assert isinstance(result, str), "Result should be string"
        
        # Test stop monitoring  
        result = monitor.stop_monitoring()
        print(f"✅ Stop monitoring: {result}")
        assert isinstance(result, str), "Result should be string"
        
        # Test status functions
        status = monitor.get_positions_status()
        print(f"✅ Positions status: {status['total_positions']} positions")
        
        alerts = monitor.get_recent_alerts(3)
        print(f"✅ Recent alerts: {len(alerts)} alerts")
        
    except Exception as e:
        print(f"❌ Monitoring test failed: {e}")
        return False
    
    return True

def test_automation_functions():
    """Test automation functions"""
    print("\n🤖 Testing Automation Functions")
    print("-" * 35)
    
    try:
        from core.automation_control import get_automation, show_automation_control_panel
        
        automation = get_automation()
        
        # Test automation methods
        result = automation.start_automation("conservative")
        print(f"✅ Start automation: {result}")
        assert isinstance(result, str), "Result should be string"
        
        result = automation.stop_automation()
        print(f"✅ Stop automation: {result}")
        assert isinstance(result, str), "Result should be string"
        
        # Test register_callback (this was causing the error)
        automation.register_callback("test", lambda x: None)
        print("✅ Register callback works")
        
        status = automation.get_automation_status()
        print(f"✅ Automation status: {status['is_running']}")
        
    except Exception as e:
        print(f"❌ Automation test failed: {e}")
        return False
    
    return True

def test_dashboard_functions():
    """Test dashboard functions"""
    print("\n🖥️ Testing Dashboard Functions")
    print("-" * 35)
    
    try:
        from core.live_dashboard import get_dashboard
        
        dashboard = get_dashboard()
        print("✅ Dashboard instance created")
        
        # Test that it has the required methods
        assert hasattr(dashboard, 'create_dashboard'), "Dashboard should have create_dashboard method"
        print("✅ Dashboard methods available")
        
    except Exception as e:
        print(f"❌ Dashboard test failed: {e}")
        return False
    
    return True

def test_trading_functions():
    """Test trading functions"""
    print("\n🚀 Testing Trading Functions")
    print("-" * 35)
    
    try:
        from trading.alpaca_trading import get_paper_trader, get_live_trader
        
        paper_trader = get_paper_trader()
        print("✅ Paper trader instance created")
        
        live_trader = get_live_trader()
        print("✅ Live trader instance created")
        
        # Test account info (will use fallback)
        account = paper_trader.get_account_info()
        buying_power = account.get('buying_power', 0)
        if isinstance(buying_power, str):
            buying_power = float(buying_power)
        print(f"✅ Account info: ${buying_power:.2f} buying power")
        
    except Exception as e:
        print(f"❌ Trading test failed: {e}")
        return False
    
    return True

def test_interface_integration():
    """Test interface integration functions"""
    print("\n🔗 Testing Interface Integration")
    print("-" * 35)
    
    try:
        # Test the exact functions the interface calls
        from core.real_time_monitor import start_real_time_monitoring, stop_real_time_monitoring
        from core.automation_control import start_automation_conservative, stop_automation
        from core.incite_ai_endpoints import get_system_status_formatted
        
        # Test monitoring
        result = start_real_time_monitoring()
        print(f"✅ Interface start monitoring: {result[:50]}...")
        assert isinstance(result, str), "Should return string"
        
        result = stop_real_time_monitoring()
        print(f"✅ Interface stop monitoring: {result[:50]}...")
        assert isinstance(result, str), "Should return string"
        
        # Test automation
        result = start_automation_conservative()
        print(f"✅ Interface start automation: {result[:50]}...")
        assert isinstance(result, str), "Should return string"
        
        result = stop_automation()
        print(f"✅ Interface stop automation: {result[:50]}...")
        assert isinstance(result, str), "Should return string"
        
        # Test system status
        status = get_system_status_formatted()
        print(f"✅ System status: {status[:50]}...")
        assert isinstance(status, str), "Should return string"
        
    except Exception as e:
        print(f"❌ Interface integration test failed: {e}")
        return False
    
    return True

def test_error_scenarios():
    """Test error handling scenarios"""
    print("\n⚠️  Testing Error Handling")
    print("-" * 35)
    
    try:
        from core.real_time_monitor import get_monitor
        
        monitor = get_monitor()
        
        # Test with different alert formats
        monitor.add_alert("Test string alert", "INFO")
        monitor.alerts.append("Raw string alert")  # This could cause string indexing errors
        
        # Test getting alerts with mixed formats
        alerts = monitor.get_recent_alerts(5)
        print(f"✅ Mixed alert formats handled: {len(alerts)} alerts")
        
        # Test monitoring status with mixed alerts
        from core.real_time_monitor import get_monitoring_status
        status = get_monitoring_status()
        print(f"✅ Monitoring status with mixed alerts: {status[:50]}...")
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False
    
    return True

def main():
    """Main test function"""
    print("🎉 FINAL INTERFACE ERROR FIXES TEST")
    print("🖥️  Testing All Button Functions")
    print("=" * 50)
    
    tests = [
        test_monitoring_functions,
        test_automation_functions, 
        test_dashboard_functions,
        test_trading_functions,
        test_interface_integration,
        test_error_scenarios
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n🎯 **TEST RESULTS: {passed}/{total} PASSED**")
    
    if passed == total:
        print("\n🎉 **ALL TESTS PASSED!**")
        print("\n✅ **INTERFACE READY:**")
        print("   • No more string indexing errors")
        print("   • All button functions work")
        print("   • Monitoring system stable")
        print("   • Automation system functional")
        print("   • Dashboard system ready")
        print("   • Trading integration working")
        print("   • Error handling robust")
        
        print("\n🚀 **YOUR INTERFACE SHOULD NOW WORK PERFECTLY!**")
        print("   • Click any button without errors")
        print("   • Real-time monitoring works")
        print("   • Automation panel opens")
        print("   • All features functional")
    else:
        print(f"\n⚠️  **{total - passed} TESTS FAILED**")
        print("   • Some issues may remain")
        print("   • Check error messages above")

if __name__ == "__main__":
    main()
