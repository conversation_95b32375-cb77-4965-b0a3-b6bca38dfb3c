"""Fast TTM Trading System

Optimized for speed - focuses on a curated list of high-volume, liquid stocks
that are most likely to have good TTM squeeze setups. No more waiting forever!
"""
from __future__ import annotations

import requests
from datetime import datetime
from typing import Dict, List, Optional
import statistics

from config import get_api_key
from logger_util import info, warning


class FastTTMTrading:
    """Fast TTM trading system focused on liquid, high-volume stocks."""
    
    def __init__(self):
        self.api_key = get_api_key('FMP_API_KEY')
        
        # Curated list of liquid stocks most likely to have TTM setups
        self.focus_stocks = [
            'AAPL', 'MSFT', 'NVDA', 'TSLA', 'GOOGL', 'AMZN', 'META', 'NFLX',
            'AMD', 'CRM', 'ADBE', 'PYPL', 'INTC', 'ORCL', 'UBER', 'LYFT',
            'SNAP', 'TWTR', 'ROKU', 'ZM', 'SHOP', 'SQ', 'COIN', 'HOOD'
        ]
        
        # Account settings for paper trading
        self.account_value = 10000
        self.risk_per_trade = 0.02
        self.trailing_stop_pct = 0.03
        
    def find_best_ttm_setup_fast(self) -> Optional[Dict]:
        """Find best TTM setup quickly by focusing on liquid stocks."""
        info("🚀 Fast TTM scan - checking liquid stocks only...")
        
        best_setup = None
        best_score = 0
        
        for symbol in self.focus_stocks:
            try:
                setup = self._analyze_ttm_setup(symbol)
                if setup and setup.get('score', 0) > best_score:
                    best_score = setup['score']
                    best_setup = setup
                    
                # If we find a really good setup (80+), take it immediately
                if best_score >= 80:
                    info(f"🎯 Excellent setup found: {symbol} (Score: {best_score})")
                    break
                    
            except Exception as e:
                warning(f"Error analyzing {symbol}: {e}")
                continue
        
        return best_setup if best_score >= 60 else None  # Minimum quality threshold
    
    def _analyze_ttm_setup(self, symbol: str) -> Optional[Dict]:
        """Quick TTM analysis for a single symbol."""
        try:
            # Get 15min data (most reliable for TTM)
            url = f"https://financialmodelingprep.com/api/v3/historical-chart/15min/{symbol}?apikey={self.api_key}"
            response = requests.get(url, timeout=10)
            data = response.json()
            
            if not data or len(data) < 30:
                return None
            
            # Get recent bars
            recent_bars = data[:30]  # Last 30 bars
            
            # Extract price data
            closes = [float(bar['close']) for bar in recent_bars]
            highs = [float(bar['high']) for bar in recent_bars]
            lows = [float(bar['low']) for bar in recent_bars]
            volumes = [float(bar['volume']) for bar in recent_bars]
            
            current_price = closes[0]
            
            # Quick TTM calculation
            sma_20 = statistics.mean(closes[:20])
            std_dev = statistics.stdev(closes[:20])
            
            # Bollinger Bands
            bb_upper = sma_20 + (2 * std_dev)
            bb_lower = sma_20 - (2 * std_dev)
            bb_width = bb_upper - bb_lower
            
            # Simple Keltner Channel approximation
            atr = statistics.mean([(h - l) for h, l in zip(highs[:20], lows[:20])])
            kc_upper = sma_20 + (1.5 * atr)
            kc_lower = sma_20 - (1.5 * atr)
            kc_width = kc_upper - kc_lower
            
            # Squeeze detection
            in_squeeze = bb_width < kc_width
            
            # Volume analysis
            avg_volume = statistics.mean(volumes[1:11])  # Exclude current bar
            current_volume = volumes[0]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            # Momentum (simplified)
            momentum = (current_price - sma_20) / sma_20
            
            # Score the setup
            score = self._score_setup_fast(in_squeeze, volume_ratio, momentum, bb_width, current_price)
            
            if score >= 60:  # Only return decent setups
                return {
                    'symbol': symbol,
                    'price': current_price,
                    'score': score,
                    'in_squeeze': in_squeeze,
                    'volume_ratio': volume_ratio,
                    'momentum': momentum,
                    'bb_width': bb_width,
                    'timeframe': '15min',
                    'squeeze_quality': 'HIGH' if score >= 80 else 'MEDIUM' if score >= 70 else 'LOW'
                }
            
            return None
            
        except Exception as e:
            warning(f"Error analyzing {symbol}: {e}")
            return None
    
    def _score_setup_fast(self, in_squeeze: bool, volume_ratio: float, momentum: float, bb_width: float, price: float) -> int:
        """Fast scoring algorithm for TTM setups."""
        score = 50  # Base score
        
        # Squeeze bonus
        if in_squeeze:
            score += 20
        
        # Volume bonus
        if volume_ratio > 2.0:
            score += 15
        elif volume_ratio > 1.5:
            score += 10
        elif volume_ratio > 1.2:
            score += 5
        
        # Momentum bonus
        if abs(momentum) > 0.02:  # Strong momentum
            score += 10
        elif abs(momentum) > 0.01:  # Moderate momentum
            score += 5
        
        # Volatility bonus (tighter squeeze = higher score)
        if bb_width < price * 0.05:  # Very tight
            score += 15
        elif bb_width < price * 0.08:  # Moderately tight
            score += 10
        
        # Price level bonus (avoid penny stocks)
        if price > 50:
            score += 5
        if price > 100:
            score += 5
        
        return min(score, 100)
    
    def execute_fast_paper_trade(self, setup: Dict) -> str:
        """Execute a paper trade quickly."""
        symbol = setup['symbol']
        price = setup['price']
        score = setup['score']
        
        # Calculate position
        risk_amount = self.account_value * self.risk_per_trade  # $200
        stop_distance = price * self.trailing_stop_pct  # 3%
        position_size = min(risk_amount / stop_distance, 1000)  # Max $1000 position
        shares = max(1, int(position_size / price))  # At least 1 share
        
        # Calculate levels
        entry_price = price
        stop_loss = price * (1 - self.trailing_stop_pct)
        take_profit = price + (2 * (price - stop_loss))  # 2:1 R/R
        
        # Risk/reward calculation
        risk_per_share = entry_price - stop_loss
        reward_per_share = take_profit - entry_price
        total_risk = shares * risk_per_share
        total_reward = shares * reward_per_share
        
        result = f"🎯 FAST TTM TRADE EXECUTED!\n\n"
        result += f"📊 SETUP ANALYSIS:\n"
        result += f"Symbol: {symbol}\n"
        result += f"Entry Price: ${entry_price:.2f}\n"
        result += f"Setup Score: {score}/100 ({setup['squeeze_quality']})\n"
        result += f"Timeframe: {setup['timeframe']}\n"
        result += f"In Squeeze: {'✅ YES' if setup['in_squeeze'] else '❌ NO'}\n"
        result += f"Volume: {setup['volume_ratio']:.1f}x average\n"
        result += f"Momentum: {setup['momentum']:+.2%}\n\n"
        
        result += f"💰 PAPER TRADE DETAILS:\n"
        result += f"Action: BUY {shares:,} shares\n"
        result += f"Position Value: ${shares * entry_price:,.2f}\n"
        result += f"Stop Loss: ${stop_loss:.2f} (3% trailing)\n"
        result += f"Take Profit: ${take_profit:.2f}\n"
        result += f"Risk: ${total_risk:.2f}\n"
        result += f"Potential Reward: ${total_reward:.2f}\n"
        result += f"Risk/Reward Ratio: 1:{total_reward/total_risk:.1f}\n\n"
        
        result += f"🎯 TRADE MANAGEMENT:\n"
        result += f"• Trailing stop will move up as price rises\n"
        result += f"• Exit at take profit or stop loss\n"
        result += f"• Monitor for volume confirmation\n"
        result += f"• This is a PAPER TRADE for testing\n\n"
        
        result += f"💡 SETUP REASONING:\n"
        if setup['in_squeeze']:
            result += f"• Stock is in TTM squeeze - low volatility before breakout\n"
        else:
            result += f"• Stock recently broke out of squeeze\n"
        
        if setup['volume_ratio'] > 1.5:
            result += f"• High volume ({setup['volume_ratio']:.1f}x) confirms interest\n"
        
        if abs(setup['momentum']) > 0.01:
            result += f"• Strong momentum suggests directional move\n"
        
        result += f"• 15min timeframe provides good risk/reward balance"
        
        return result


def find_and_trade_ttm_fast() -> str:
    """Find and trade the best TTM setup quickly."""
    try:
        trader = FastTTMTrading()
        
        # Find best setup (should be much faster)
        setup = trader.find_best_ttm_setup_fast()
        
        if not setup:
            return "⏳ No qualifying TTM setups found in liquid stocks right now. Try again in 15-30 minutes or check the TTM Scanner for more options."
        
        # Execute the trade
        return trader.execute_fast_paper_trade(setup)
        
    except Exception as e:
        return f"❌ Error in fast TTM trading: {str(e)}"


if __name__ == "__main__":
    print("🚀 FAST TTM TRADING SYSTEM")
    print("=" * 40)
    print("Optimized for speed - focuses on liquid stocks only")
    print("=" * 40)
    
    result = find_and_trade_ttm_fast()
    print(result)
    
    print(f"\n💡 ADVANTAGES OF FAST SYSTEM:")
    print("• Scans only 24 liquid, high-volume stocks")
    print("• Takes 10-30 seconds instead of minutes")
    print("• Focuses on stocks most likely to have good setups")
    print("• Uses 15min timeframe for optimal signals")
    print("• Immediate execution when good setup found")
