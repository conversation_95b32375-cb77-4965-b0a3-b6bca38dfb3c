"""Advanced Market Intelligence System

Handles complex, multi-layered questions like:
- "What insider trades are there that are recent and are close to their current price?"
- "Show me stocks with unusual volume and recent insider buying"
- "Find companies with earnings beats and institutional accumulation"

This system can break down complex questions and provide comprehensive answers.
"""
from __future__ import annotations

import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import statistics

from config import get_api_key
from logger_util import info, warning

FMP_BASE = "https://financialmodelingprep.com/api/v3"


class AdvancedMarketIntelligence:
    """Advanced market intelligence for complex multi-layered questions."""
    
    def __init__(self):
        self.api_key = get_api_key('FMP_API_KEY')
        if not self.api_key:
            raise ValueError("FMP_API_KEY not found in environment")
    
    def _fmp_get(self, endpoint: str) -> Any:
        """Make FMP API request."""
        try:
            url = f"{FMP_BASE}{endpoint}&apikey={self.api_key}"
            response = requests.get(url, timeout=15)
            return response.json()
        except Exception as e:
            warning(f"FMP API error: {e}")
            return []
    
    def get_recent_insider_trades(self, days: int = 30, limit: int = 500) -> List[Dict]:
        """Get recent insider trades across all stocks using paid tier endpoints."""
        try:
            # With paid tier, we can get more comprehensive data
            all_trades = []

            # Try multiple pages to get more data (paid tier allows this)
            for page in range(0, 5):  # Get first 5 pages
                insider_data = self._fmp_get(f"/insider-trading?page={page}&limit=100")

                if not insider_data:
                    break

                all_trades.extend(insider_data)

                # If we got less than 100 results, we've reached the end
                if len(insider_data) < 100:
                    break

            if not all_trades:
                # Try alternative endpoint for paid tier
                insider_data = self._fmp_get(f"/insider-trading-rss-feed?page=0&limit={limit}")
                if insider_data:
                    all_trades = insider_data

            # Filter for recent trades
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_trades = []

            for trade in all_trades:
                try:
                    # Try different date fields that might be available
                    date_field = trade.get('filingDate') or trade.get('transactionDate') or trade.get('date')
                    if date_field:
                        trade_date = datetime.strptime(date_field, '%Y-%m-%d')
                        if trade_date >= cutoff_date:
                            recent_trades.append(trade)
                except (ValueError, TypeError):
                    continue

            info(f"Found {len(recent_trades)} recent insider trades from {len(all_trades)} total trades")
            return recent_trades

        except Exception as e:
            warning(f"Error getting insider trades: {e}")
            return []
    
    def analyze_insider_trades_vs_current_price(self, days: int = 30) -> List[Dict]:
        """Find insider trades close to current stock prices."""
        info(f"🔍 Analyzing insider trades vs current prices (last {days} days)")
        
        # Get recent insider trades
        insider_trades = self.get_recent_insider_trades(days)
        
        if not insider_trades:
            return []
        
        # Group trades by symbol
        trades_by_symbol = {}
        for trade in insider_trades:
            symbol = trade.get('symbol', '').upper()
            if symbol:
                if symbol not in trades_by_symbol:
                    trades_by_symbol[symbol] = []
                trades_by_symbol[symbol].append(trade)
        
        results = []
        
        # Analyze each symbol
        for symbol, trades in trades_by_symbol.items():
            try:
                # Get current price
                quote_data = self._fmp_get(f"/quote/{symbol}?")
                if not quote_data:
                    continue
                
                current_price = float(quote_data[0].get('price', 0))
                if current_price <= 0:
                    continue
                
                # Analyze trades for this symbol
                purchases = [t for t in trades if t.get('transactionType') == 'P-Purchase']
                sales = [t for t in trades if t.get('transactionType') == 'S-Sale']
                
                if not purchases and not sales:
                    continue
                
                # Calculate average trade prices
                purchase_prices = []
                sale_prices = []
                
                for trade in purchases:
                    price = float(trade.get('price', 0))
                    if price > 0:
                        purchase_prices.append(price)
                
                for trade in sales:
                    price = float(trade.get('price', 0))
                    if price > 0:
                        sale_prices.append(price)
                
                # Calculate metrics
                avg_purchase_price = statistics.mean(purchase_prices) if purchase_prices else 0
                avg_sale_price = statistics.mean(sale_prices) if sale_prices else 0
                
                # Calculate how close trades are to current price
                purchase_diff = abs(current_price - avg_purchase_price) / current_price * 100 if avg_purchase_price > 0 else 100
                sale_diff = abs(current_price - avg_sale_price) / current_price * 100 if avg_sale_price > 0 else 100
                
                # Net insider activity
                total_purchase_value = sum(float(t.get('securitiesTransacted', 0)) * float(t.get('price', 0)) for t in purchases)
                total_sale_value = sum(float(t.get('securitiesTransacted', 0)) * float(t.get('price', 0)) for t in sales)
                net_insider_value = total_purchase_value - total_sale_value
                
                # Only include if trades are reasonably close to current price (within 20%)
                if purchase_diff <= 20 or sale_diff <= 20:
                    results.append({
                        'symbol': symbol,
                        'current_price': round(current_price, 2),
                        'purchases': len(purchases),
                        'sales': len(sales),
                        'avg_purchase_price': round(avg_purchase_price, 2) if avg_purchase_price > 0 else None,
                        'avg_sale_price': round(avg_sale_price, 2) if avg_sale_price > 0 else None,
                        'purchase_price_diff_pct': round(purchase_diff, 1) if avg_purchase_price > 0 else None,
                        'sale_price_diff_pct': round(sale_diff, 1) if avg_sale_price > 0 else None,
                        'net_insider_value': round(net_insider_value, 0),
                        'insider_sentiment': 'Bullish' if net_insider_value > 0 else 'Bearish' if net_insider_value < 0 else 'Neutral',
                        'recent_trades': trades[:3]  # Show most recent 3 trades
                    })
                    
            except Exception as e:
                warning(f"Error analyzing {symbol}: {e}")
                continue
        
        # Sort by net insider value (most bullish first)
        results.sort(key=lambda x: x['net_insider_value'], reverse=True)
        
        return results
    
    def find_unusual_volume_with_insider_activity(self, days: int = 7) -> List[Dict]:
        """Find stocks with unusual volume AND recent insider activity."""
        info(f"🔍 Finding unusual volume + insider activity (last {days} days)")
        
        # Get stocks with recent insider activity
        insider_results = self.analyze_insider_trades_vs_current_price(days)
        
        enhanced_results = []
        
        for result in insider_results:
            symbol = result['symbol']
            
            try:
                # Get historical volume data
                historical_data = self._fmp_get(f"/historical-price-full/{symbol}?")
                
                if not historical_data or 'historical' not in historical_data:
                    continue
                
                hist_data = historical_data['historical'][:30]  # Last 30 days
                
                if len(hist_data) < 10:
                    continue
                
                # Calculate average volume
                volumes = [float(day.get('volume', 0)) for day in hist_data[1:]]  # Exclude today
                avg_volume = statistics.mean(volumes) if volumes else 0
                
                # Get today's volume
                today_volume = float(hist_data[0].get('volume', 0))
                
                # Calculate volume ratio
                volume_ratio = today_volume / avg_volume if avg_volume > 0 else 0
                
                # Only include if volume is significantly higher (1.5x or more)
                if volume_ratio >= 1.5:
                    result['avg_volume'] = int(avg_volume)
                    result['today_volume'] = int(today_volume)
                    result['volume_ratio'] = round(volume_ratio, 1)
                    result['volume_status'] = 'Unusual' if volume_ratio >= 2.0 else 'Elevated'
                    enhanced_results.append(result)
                    
            except Exception as e:
                warning(f"Error analyzing volume for {symbol}: {e}")
                continue
        
        # Sort by volume ratio (highest unusual volume first)
        enhanced_results.sort(key=lambda x: x['volume_ratio'], reverse=True)
        
        return enhanced_results
    
    def comprehensive_market_scan(self, query_type: str = "insider_trades_near_price") -> str:
        """Perform comprehensive market scan based on query type."""
        
        if query_type == "insider_trades_near_price":
            results = self.analyze_insider_trades_vs_current_price(30)
            
            if not results:
                return "📊 No recent insider trades found close to current prices in the last 30 days."
            
            report = "🎯 INSIDER TRADES CLOSE TO CURRENT PRICES\n"
            report += "=" * 50 + "\n\n"
            
            # Bullish insider activity
            bullish = [r for r in results if r['net_insider_value'] > 0]
            if bullish:
                report += "📈 BULLISH INSIDER ACTIVITY:\n"
                report += "-" * 30 + "\n"
                for result in bullish[:5]:
                    report += f"🟢 {result['symbol']} - ${result['current_price']:.2f}\n"
                    if result['avg_purchase_price']:
                        report += f"   Insider Purchases: {result['purchases']} trades @ avg ${result['avg_purchase_price']:.2f}\n"
                        report += f"   Price Difference: {result['purchase_price_diff_pct']:.1f}% from current\n"
                    report += f"   Net Insider Value: ${result['net_insider_value']:,.0f}\n"
                    report += f"   Sentiment: {result['insider_sentiment']}\n\n"
            
            # Bearish insider activity
            bearish = [r for r in results if r['net_insider_value'] < 0]
            if bearish:
                report += "📉 BEARISH INSIDER ACTIVITY:\n"
                report += "-" * 30 + "\n"
                for result in bearish[:3]:
                    report += f"🔴 {result['symbol']} - ${result['current_price']:.2f}\n"
                    if result['avg_sale_price']:
                        report += f"   Insider Sales: {result['sales']} trades @ avg ${result['avg_sale_price']:.2f}\n"
                        report += f"   Price Difference: {result['sale_price_diff_pct']:.1f}% from current\n"
                    report += f"   Net Insider Value: ${result['net_insider_value']:,.0f}\n\n"
            
            return report
            
        elif query_type == "unusual_volume_insider":
            results = self.find_unusual_volume_with_insider_activity(7)
            
            if not results:
                return "📊 No stocks found with both unusual volume and recent insider activity."
            
            report = "🎯 UNUSUAL VOLUME + INSIDER ACTIVITY\n"
            report += "=" * 40 + "\n\n"
            
            for result in results[:10]:
                report += f"📊 {result['symbol']} - ${result['current_price']:.2f}\n"
                report += f"   Volume: {result['today_volume']:,} ({result['volume_ratio']:.1f}x avg)\n"
                report += f"   Status: {result['volume_status']} Volume\n"
                report += f"   Insider Activity: {result['insider_sentiment']}\n"
                if result['net_insider_value'] != 0:
                    report += f"   Net Insider Value: ${result['net_insider_value']:,.0f}\n"
                report += "\n"
            
            return report
        
        else:
            return "❌ Unknown query type. Available: 'insider_trades_near_price', 'unusual_volume_insider'"


def analyze_insider_trades_near_price() -> str:
    """Analyze insider trades close to current stock prices."""
    try:
        intelligence = AdvancedMarketIntelligence()
        return intelligence.comprehensive_market_scan("insider_trades_near_price")
    except Exception as e:
        return f"❌ Error analyzing insider trades: {str(e)}"


def find_unusual_volume_with_insider_buying() -> str:
    """Find stocks with unusual volume and recent insider buying."""
    try:
        intelligence = AdvancedMarketIntelligence()
        return intelligence.comprehensive_market_scan("unusual_volume_insider")
    except Exception as e:
        return f"❌ Error finding unusual volume with insider activity: {str(e)}"


if __name__ == "__main__":
    print("🧠 Testing Advanced Market Intelligence")
    print("=" * 50)
    
    # Test insider trades near current price
    print("\n1️⃣ TESTING: Insider trades close to current prices")
    result1 = analyze_insider_trades_near_price()
    print(result1)
    
    print("\n" + "=" * 50)
    
    # Test unusual volume with insider activity
    print("\n2️⃣ TESTING: Unusual volume + insider activity")
    result2 = find_unusual_volume_with_insider_buying()
    print(result2)
