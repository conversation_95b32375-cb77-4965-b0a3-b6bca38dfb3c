"""Launch script for Ultimate Trading Expert v2 (CLI interface).

This is an MVP chat loop that shows module banners and can handle simple
intents: profit_target, market_analysis, unknown.

Usage:
    python launch_ultimate_expert_v2.py
"""
from __future__ import annotations

import asyncio
import json
import sys
from pathlib import Path
from typing import List

import requests

from config import dump_env_details, public_keys_summary, get_api_key  # noqa: E402
from logger_util import info  # noqa: E402
from rate_limiter import _limiters  # noqa: E402, WPS433 – internal import OK in CLI
from chat_core import chat_gpt  # noqa: E402
from alpaca.trading.client import TradingClient
from alpaca.common.exceptions import APIError

BANNER = (
    "\n==============================\n"
    " 🤖  Ultimate Trading Expert  \n"
    "==============================\n"
)


def _print_startup() -> None:
    print(BANNER)
    env = dump_env_details()
    info("🎛️  Environment", **env)
    # Show masked keys for user verification
    print("🔑 Keys loaded:")
    for k, v in public_keys_summary().items():
        print(f"   {k}: {v}")

    # Quick rate-limiter health check (sync)
    for name, lim in _limiters.items():
        lim.acquire_sync()
        print(f"✅ Rate-limit bucket '{name}' OK (limit {lim.limit}/min)")

    # Alpaca paper account health check
    try:
        client = TradingClient(get_api_key("ALPACA_API_KEY"), get_api_key("ALPACA_API_SECRET"), paper=True)
        acct = client.get_account()
        print(f"✅ Alpaca paper account active – Buying power: ${acct.buying_power}")
    except APIError as exc:
        print("⚠️  Alpaca paper API connection failed:", exc)
        print("   📋 Possible solutions:")
        print("   1. Verify your Alpaca account is approved for paper trading")
        print("   2. Check if keys are for live vs paper trading")
        print("   3. Ensure your IP is whitelisted (if required)")
        print("   4. Verify keys in your Alpaca dashboard")
        print("   ➜ Continuing without live trading (analysis features still work)")
        print()


async def chat_loop() -> None:
    while True:
        try:
            user_input = input("\n💬 You: ").strip()
        except (EOFError, KeyboardInterrupt):
            print("\n👋 Goodbye! Stay safe in the markets.")
            break

        if not user_input:
            continue
        answer = chat_gpt(user_input)
        print(f"\n{answer}\n")


def main() -> None:
    _print_startup()
    try:
        asyncio.run(chat_loop())
    except RuntimeError:  # Nested event loop (e.g. Jupyter)
        loop = asyncio.get_event_loop()
        loop.run_until_complete(chat_loop())


if __name__ == "__main__":
    main()