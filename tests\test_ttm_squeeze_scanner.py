#!/usr/bin/env python3
"""
Test script for the Advanced TTM Squeeze Scanner.

This script tests the scanner with a small set of symbols to verify it works correctly.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from advanced_ttm_squeeze_scanner import AdvancedTTMSqueezeScanner, run_ttm_squeeze_scan
    from logger_util import info, warning, error
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure all dependencies are installed:")
    print("   pip install -r requirements.txt")
    sys.exit(1)


def test_basic_functionality():
    """Test basic scanner functionality."""
    print("🔍 Testing Advanced TTM Squeeze Scanner")
    print("=" * 50)
    
    # Test with a small set of symbols
    test_symbols = ['AAPL', 'MSFT', 'PLTR', 'NVDA', 'TSLA']
    test_timeframes = ['5min', '15min', '1hour']
    
    print(f"📊 Testing with symbols: {test_symbols}")
    print(f"⏰ Testing timeframes: {test_timeframes}")
    print(f"🕐 Scan started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)
    
    try:
        # Run the scan
        results = run_ttm_squeeze_scan(
            symbols=test_symbols,
            timeframes=test_timeframes,
            max_results=10
        )
        
        # Display results
        if "error" in results:
            print(f"❌ Error: {results['error']}")
            if "suggestion" in results:
                print(f"💡 Suggestion: {results['suggestion']}")
            return False
        else:
            summary = results.get("scan_summary", {})
            setups = results.get("top_setups", [])
            
            print(f"✅ Scan completed successfully!")
            print(f"📈 Total setups found: {summary.get('total_setups_found', 0)}")
            print(f"🎯 Showing top: {summary.get('showing_top', 0)}")
            print(f"⏰ Scan time: {summary.get('scan_timestamp', 'N/A')}")
            
            if setups:
                print(f"\n🏆 Top TTM Squeeze Setups:")
                print("-" * 70)
                
                for i, setup in enumerate(setups[:5], 1):  # Show top 5
                    print(f"{i}. {setup['symbol']} ({setup['timeframe']}) - Grade: {setup['grade']} ({setup['confidence']})")
                    print(f"   💰 Entry: {setup['entry_price']} | Target: {setup['target_price']} | Stop: {setup['stop_loss']}")
                    print(f"   📊 R:R = {setup['risk_reward']} | Signal: {setup['signal_time']}")
                    
                    # Show pattern confirmation
                    conf = setup['pattern_confirmation']
                    print(f"   ✅ Pattern: Release:{conf['squeeze_released']} Build:{conf['histogram_build']} "
                          f"EMA:{conf['ema8_rising']} Mom:{conf['momentum_rising']} Price:{conf['price_above_5ema']}")
                    print()
            else:
                print("\n📝 No setups found matching the strict PLTR pattern criteria")
                print("💡 This is normal - the pattern requires very specific conditions:")
                print("   • Squeeze release (BBands outside Keltner)")
                print("   • Histogram build (3 rising after ≥4 down)")
                print("   • EMA/momentum confirmation")
                print("   • Price above 5-EMA")
                print("   • SqueezeLine >70%")
            
            return True
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_individual_components():
    """Test individual scanner components."""
    print("\n🔧 Testing Individual Components")
    print("=" * 50)
    
    try:
        scanner = AdvancedTTMSqueezeScanner()
        print("✅ Scanner initialization: OK")
        
        # Test large cap stock fetching
        print("📈 Testing large cap stock fetching...")
        large_caps = scanner._get_fallback_large_caps()
        print(f"✅ Fallback large caps: {len(large_caps)} symbols")
        print(f"   Sample: {large_caps[:10]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Component test failed: {e}")
        return False


def test_data_fetching():
    """Test data fetching capabilities."""
    print("\n📊 Testing Data Fetching")
    print("=" * 50)
    
    async def fetch_test():
        try:
            scanner = AdvancedTTMSqueezeScanner()
            
            # Test data fetching for AAPL 5min
            print("📈 Testing AAPL 5min data fetching...")
            df = await scanner.get_intraday_data('AAPL', '5min', 100)
            
            if df.empty:
                print("⚠️  No data returned (may be outside market hours)")
                return False
            else:
                print(f"✅ Data fetched: {len(df)} bars")
                print(f"   Columns: {list(df.columns)}")
                print(f"   Date range: {df['datetime'].min()} to {df['datetime'].max()}")
                
                # Test indicator computation
                print("🔢 Testing indicator computation...")
                df_with_indicators = scanner.compute_ttm_squeeze_indicators(df)
                
                indicator_cols = ['bb_upper', 'bb_lower', 'kc_upper', 'kc_lower', 
                                'ema5', 'ema8', 'ema21', 'momentum', 'squeeze_hist', 'squeeze_line']
                
                missing_cols = [col for col in indicator_cols if col not in df_with_indicators.columns]
                if missing_cols:
                    print(f"⚠️  Missing indicator columns: {missing_cols}")
                    return False
                else:
                    print("✅ All indicators computed successfully")
                    return True
                    
        except Exception as e:
            print(f"❌ Data fetching test failed: {e}")
            return False
    
    try:
        return asyncio.run(fetch_test())
    except Exception as e:
        print(f"❌ Async test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Advanced TTM Squeeze Scanner Test Suite")
    print("=" * 60)
    print("Testing the exact pattern from PLTR screenshot:")
    print("✓ Squeeze release (BBands outside Keltner)")
    print("✓ Histogram build (3 rising after ≥4 down)")
    print("✓ EMA/momentum confirmation")
    print("✓ Price above 5-EMA")
    print("✓ SqueezeLine >70%")
    print("=" * 60)
    
    tests = [
        ("Component Tests", test_individual_components),
        ("Data Fetching Tests", test_data_fetching),
        ("Full Scanner Tests", test_basic_functionality),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            success = test_func()
            results.append((test_name, success))
            if success:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Scanner is ready for use.")
        print("\n💡 Next steps:")
        print("   1. Install missing dependencies: pip install -r requirements.txt")
        print("   2. Run during market hours for live data")
        print("   3. Integrate with chat system")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        print("\n💡 Common issues:")
        print("   1. Missing dependencies (TA-Lib, aiohttp)")
        print("   2. API key not configured")
        print("   3. Outside market hours (no recent data)")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
