#!/usr/bin/env python3
"""
S&P 500 TTM Scanner Diagnostic Tool
Diagnoses why the dashboard isn't showing S&P 500 opportunities
"""
import sys
import os

# Add paths
sys.path.insert(0, os.getcwd())

def test_api_connection():
    """Test FMP API connection"""
    print("🔍 1. TESTING FMP API CONNECTION")
    print("=" * 40)
    
    import requests
    api_key = 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'
    test_url = f'https://financialmodelingprep.com/api/v3/historical-chart/15min/AAPL?apikey={api_key}'
    
    try:
        response = requests.get(test_url, timeout=10)
        print(f"✅ API Response: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Data records: {len(data)}")
            return True
        else:
            print(f"❌ API Error: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"❌ Connection Error: {e}")
        return False

def test_ttm_scanner():
    """Test TTM scanner with multiple symbols"""
    print("\n🔍 2. TESTING TTM SCANNER WITH S&P 500 SYMBOLS")
    print("=" * 50)
    
    try:
        from scanners.proper_ttm_squeeze_scanner import ProperTTMSqueezeScanner
        scanner = ProperTTMSqueezeScanner()
        print(f"✅ Scanner initialized with API key: {scanner.api_key[:10]}...{scanner.api_key[-5:]}")
        
        test_symbols = ['AAPL', 'MSFT', 'NVDA', 'PLTR', 'TSLA', 'GOOGL', 'AMZN', 'META', 'JPM', 'V']
        timeframes = ['15min', '1hour']
        
        all_results = []
        
        for symbol in test_symbols:
            print(f"\n📊 Testing {symbol}:")
            for tf in timeframes:
                try:
                    result = scanner.scan_symbol(symbol, tf)
                    if result:
                        grade = result['grade']
                        confidence = result['confidence'] * 100
                        price = result['entry_price']
                        print(f"  ✅ {tf}: Grade {grade} ({confidence:.0f}%) - ${price:.2f}")
                        all_results.append(result)
                    else:
                        print(f"  ❌ {tf}: No TTM opportunity")
                except Exception as e:
                    print(f"  ❌ {tf}: Error - {str(e)[:50]}")
        
        return all_results
        
    except Exception as e:
        print(f"❌ Scanner Error: {e}")
        return []

def analyze_results(results):
    """Analyze TTM scan results"""
    print(f"\n🔍 3. ANALYZING SCAN RESULTS")
    print("=" * 30)
    
    if not results:
        print("❌ No opportunities found!")
        return
    
    print(f"✅ Total opportunities found: {len(results)}")
    
    # Grade distribution
    grades = {}
    for r in results:
        grade = r['grade']
        grades[grade] = grades.get(grade, 0) + 1
    
    print(f"\n📊 Grade Distribution:")
    for grade, count in sorted(grades.items()):
        print(f"   {grade}: {count} opportunities")
    
    # Filter for dashboard criteria
    high_grade = [r for r in results if r['grade'] in ['A+', 'A', 'B+'] and r['confidence'] >= 0.75]
    print(f"\n🎯 Dashboard-worthy opportunities (A+/A/B+, 75%+ confidence): {len(high_grade)}")
    
    for r in high_grade:
        symbol = r['symbol']
        tf = r['timeframe']
        grade = r['grade']
        conf = r['confidence'] * 100
        price = r['entry_price']
        print(f"   📈 {symbol} ({tf}): Grade {grade} ({conf:.0f}%) - ${price:.2f}")
    
    return high_grade

def test_dashboard_integration():
    """Test dashboard integration"""
    print(f"\n🔍 4. TESTING DASHBOARD INTEGRATION")
    print("=" * 35)
    
    try:
        from scanners.sp500_ttm_batch_scanner import run_sp500_batch_scan
        import asyncio
        
        print("🚀 Running S&P 500 batch scan...")
        
        # Run async scan
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        scan_result = loop.run_until_complete(run_sp500_batch_scan(['15min'], priority_first=True))
        loop.close()
        
        print(f"✅ Batch scan completed")
        print(f"📊 Result type: {type(scan_result)}")
        
        if isinstance(scan_result, dict):
            print(f"📊 Result keys: {list(scan_result.keys())}")
            
            if 'top_opportunities' in scan_result:
                opportunities = scan_result['top_opportunities']
                print(f"✅ Top opportunities: {len(opportunities)}")
                
                for i, opp in enumerate(opportunities[:5]):
                    symbol = opp.get('symbol', 'N/A')
                    grade = opp.get('grade', 'N/A')
                    conf = opp.get('confidence', 0)
                    print(f"   {i+1}. {symbol}: Grade {grade} ({conf:.0f}%)")
                
                return opportunities
            else:
                print("❌ No 'top_opportunities' in result")
        else:
            print(f"❌ Unexpected result format: {scan_result}")
        
        return []
        
    except Exception as e:
        print(f"❌ Dashboard integration error: {e}")
        return []

def main():
    """Main diagnostic function"""
    print("🚀 S&P 500 TTM SCANNER DIAGNOSTIC")
    print("=" * 60)
    
    # Test 1: API Connection
    api_ok = test_api_connection()
    if not api_ok:
        print("\n❌ DIAGNOSIS: FMP API connection failed!")
        print("💡 Fix: Check API key and internet connection")
        return
    
    # Test 2: TTM Scanner
    results = test_ttm_scanner()
    
    # Test 3: Analyze Results
    dashboard_opportunities = analyze_results(results)
    
    # Test 4: Dashboard Integration
    batch_opportunities = test_dashboard_integration()
    
    # Final diagnosis
    print(f"\n🎯 FINAL DIAGNOSIS")
    print("=" * 20)
    
    if dashboard_opportunities:
        print(f"✅ TTM Scanner working: {len(dashboard_opportunities)} high-grade opportunities found")
    else:
        print("❌ Issue: No high-grade opportunities found")
        print("💡 Possible causes:")
        print("   • Market conditions: No TTM squeezes in major stocks right now")
        print("   • Grading too strict: Consider lowering grade requirements")
        print("   • Timeframe issue: Try different timeframes")
    
    if batch_opportunities:
        print(f"✅ Batch scanner working: {len(batch_opportunities)} opportunities")
    else:
        print("❌ Issue: Batch scanner not returning opportunities")
        print("💡 Fix needed in dashboard integration")
    
    print(f"\n💡 RECOMMENDATIONS:")
    if not dashboard_opportunities and results:
        print("   • Lower grade filter from A+/A/B+ to include B/C grades")
        print("   • Lower confidence threshold from 75% to 60%")
        print("   • Add more timeframes (5min, 4hour)")
    
    if not batch_opportunities:
        print("   • Fix S&P 500 batch scanner integration")
        print("   • Ensure proper data format conversion")

if __name__ == "__main__":
    main()
