"""Test Alpaca order placement after market hours."""

from alpaca_trading import AlpacaTrader

def test_alpaca_order():
    """Test placing an order through Alpaca."""
    print("🧪 Testing Alpaca Order Placement")
    print("=" * 40)
    
    try:
        # Initialize trader
        trader = AlpacaTrader(paper_trading=True)
        
        # Check market status
        market_open = trader.is_market_open()
        print(f"Market Status: {'OPEN' if market_open else 'CLOSED'}")
        
        # Get current price
        symbol = 'AAPL'
        price = trader.get_current_price(symbol)
        print(f"{symbol} Current Price: ${price:.2f}" if price else f"Could not get {symbol} price")
        
        # Test placing an order
        print(f"\nPlacing test order for {symbol}...")
        result = trader.place_order(
            symbol=symbol,
            quantity=1,
            side='buy',
            order_type='market'  # Will auto-convert to limit if market closed
        )
        
        if result['success']:
            print("✅ SUCCESS: Order placed!")
            print(f"Order ID: {result['order_id']}")
            print(f"Symbol: {symbol}")
            print(f"Quantity: 1 share")
            print(f"Side: buy")
            print("\n💡 Check your Alpaca dashboard to see the order!")
            
            # Check orders to confirm
            print("\nChecking recent orders...")
            orders = trader.get_orders('open')
            print(f"Found {len(orders)} open orders")
            
            if orders:
                latest_order = orders[0]
                print(f"Latest order: {latest_order.get('symbol')} - {latest_order.get('side')} {latest_order.get('qty')} - Status: {latest_order.get('status')}")
            
        else:
            print(f"❌ FAILED: {result['error']}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_alpaca_order()
