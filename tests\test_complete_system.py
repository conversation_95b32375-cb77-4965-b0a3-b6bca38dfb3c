"""Test Complete TTM Squeeze System

Test all components:
1. Proper TTM Scanner (finds stocks that have already broken out)
2. TTM Watchlist (finds stocks BEFORE breakout)
3. Chat integration
4. Dynamic stop losses
"""
from __future__ import annotations

from chat_core import chat_gpt
from ttm_squeeze_watchlist import run_watchlist_scan
from proper_ttm_squeeze_scanner import run_proper_ttm_scan
from profit_target import ProfitTargetPlanner

def test_complete_system():
    """Test the complete TTM squeeze system."""
    print("🚀 TESTING COMPLETE TTM SQUEEZE SYSTEM")
    print("=" * 60)
    
    # Test 1: TTM Scanner (stocks that have broken out)
    print("\n1️⃣ TESTING TTM SCANNER (Already Broken Out):")
    print("-" * 50)
    try:
        scanner_results = run_proper_ttm_scan()
        print("✅ TTM Scanner working!")
        if "HIGH GRADE OPPORTUNITIES" in scanner_results:
            lines = scanner_results.split('\n')[:10]  # Show first 10 lines
            for line in lines:
                if line.strip():
                    print(f"   {line}")
        else:
            print("   📊 No breakouts found (normal)")
    except Exception as e:
        print(f"   ❌ TTM Scanner error: {e}")
    
    # Test 2: Watchlist (stocks BEFORE breakout)
    print("\n2️⃣ TESTING WATCHLIST (Before Breakout):")
    print("-" * 50)
    try:
        watchlist_results = run_watchlist_scan()
        print("✅ Watchlist working!")
        if "BUILDING PRESSURE" in watchlist_results:
            lines = watchlist_results.split('\n')
            for line in lines:
                if "BUILDING PRESSURE" in line or "📊" in line:
                    print(f"   {line}")
                    break
        else:
            print("   📊 No squeeze candidates found")
    except Exception as e:
        print(f"   ❌ Watchlist error: {e}")
    
    # Test 3: Chat Integration
    print("\n3️⃣ TESTING CHAT INTEGRATION:")
    print("-" * 50)
    try:
        print("   Testing: 'show me stocks before breakout'")
        chat_response = chat_gpt("show me stocks before breakout")
        print("✅ Chat integration working!")
        if len(chat_response) > 100:
            print(f"   Response preview: {chat_response[:100]}...")
        else:
            print(f"   Response: {chat_response}")
    except Exception as e:
        print(f"   ❌ Chat integration error: {e}")
    
    # Test 4: Profit Target with TTM Integration
    print("\n4️⃣ TESTING PROFIT TARGET WITH TTM:")
    print("-" * 50)
    try:
        planner = ProfitTargetPlanner()
        plan = planner.plan(target_profit_dollars=50)
        print("✅ Profit target with TTM working!")
        print(f"   Symbol: {plan['symbol']}")
        print(f"   Entry: ${plan['entry_price']:.2f}")
        print(f"   Stop: ${plan['stop_loss']:.2f} ({plan.get('stop_type', 'fixed')})")
        print(f"   Target: ${plan['take_profit']:.2f}")
        print(f"   Required Cash: ${plan['required_cash']:.2f}")
    except Exception as e:
        print(f"   ❌ Profit target error: {e}")
    
    # Test 5: Dynamic Stop Loss
    print("\n5️⃣ TESTING DYNAMIC STOP LOSS:")
    print("-" * 50)
    try:
        planner = ProfitTargetPlanner()
        stop_update = planner.update_stop_loss("NVDA", 141.50, 138.50)
        print("✅ Dynamic stop loss working!")
        print(f"   Current Stop: ${stop_update['current_stop']:.2f}")
        print(f"   Recommended: ${stop_update['recommended_stop']:.2f}")
        print(f"   Should Update: {'✅ YES' if stop_update['should_update'] else '❌ NO'}")
        print(f"   Stop Type: {stop_update['stop_type']}")
    except Exception as e:
        print(f"   ❌ Dynamic stop loss error: {e}")

def show_system_summary():
    """Show system capabilities summary."""
    print("\n" + "=" * 60)
    print("🎯 SYSTEM CAPABILITIES SUMMARY")
    print("=" * 60)
    
    print("\n📊 TTM SQUEEZE DETECTION:")
    print("  • Proper TTM Scanner - finds stocks that have broken out")
    print("  • TTM Watchlist - finds stocks BEFORE breakout (the real opportunities)")
    print("  • A-F grading system for opportunity quality")
    print("  • Multiple timeframe scanning (5min, 15min, 1hour)")
    
    print("\n🤖 AI CHAT INTEGRATION:")
    print("  • 'scan for TTM squeeze setups' - shows breakouts")
    print("  • 'show me stocks before breakout' - shows watchlist")
    print("  • 'make me $50 profit today' - creates trading plan")
    print("  • 'update my stop loss for NVDA' - dynamic stop management")
    
    print("\n🛡️ DYNAMIC STOP LOSS MANAGEMENT:")
    print("  • ATR-based volatility adjustments")
    print("  • Momentum-based modifications")
    print("  • Support/resistance level integration")
    print("  • Trailing stop functionality")
    
    print("\n🖥️ GUI INTERFACE:")
    print("  • Chat tab for AI interaction")
    print("  • TTM Scanner tab with watchlist button")
    print("  • Position Manager tab for stop loss updates")
    print("  • Real-time alerts for A/B grade opportunities")
    
    print("\n🎯 KEY DIFFERENCE:")
    print("  • TTM Scanner: Shows stocks that ALREADY broke out (too late)")
    print("  • TTM Watchlist: Shows stocks BEFORE breakout (perfect timing)")
    print("  • Use watchlist to catch opportunities before they happen!")

if __name__ == "__main__":
    test_complete_system()
    show_system_summary()
    
    print("\n" + "=" * 60)
    print("🚀 TO USE THE SYSTEM:")
    print("=" * 60)
    print("1. Launch GUI: python launch_gui.py")
    print("2. Chat commands:")
    print("   • 'show me stocks before breakout' - for watchlist")
    print("   • 'make me $50 profit today' - for trading plan")
    print("3. Use 📋 Watchlist button in TTM Scanner tab")
    print("4. Monitor 'Building Pressure' stocks for breakouts")
    print("\n✅ System is ready for live trading!")
