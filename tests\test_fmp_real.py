#!/usr/bin/env python3
"""Test FMP API with the real API key."""

import requests
import json

def test_fmp_options():
    """Test FMP options API with the real key."""
    print("🔍 Testing FMP Options API with Real Key")
    print("-" * 50)
    
    api_key = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
    symbol = "AAPL"
    
    # Test different FMP endpoints
    endpoints = [
        f"https://financialmodelingprep.com/api/v3/options-chain/{symbol}",
        f"https://financialmodelingprep.com/api/v4/options-chain/{symbol}",
        f"https://financialmodelingprep.com/api/v3/options/{symbol}",
        f"https://financialmodelingprep.com/api/v3/available-options/{symbol}"
    ]
    
    for i, url in enumerate(endpoints, 1):
        print(f"\n{i}. Testing: {url}")
        
        try:
            params = {"apikey": api_key}
            response = requests.get(url, params=params, timeout=15)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    if isinstance(data, list):
                        print(f"   ✅ SUCCESS: Got {len(data)} options")
                        if data:
                            option = data[0]
                            print(f"   Sample: {option.get('symbol', 'N/A')} - ${option.get('lastPrice', 'N/A')}")
                    elif isinstance(data, dict):
                        print(f"   ✅ SUCCESS: Got dict with keys: {list(data.keys())}")
                        if "options" in data:
                            options = data["options"]
                            print(f"   Found {len(options)} options in 'options' key")
                        elif "chain" in data:
                            chain = data["chain"]
                            print(f"   Found {len(chain)} options in 'chain' key")
                    else:
                        print(f"   ⚠️ Unexpected data type: {type(data)}")
                        
                except json.JSONDecodeError:
                    print(f"   ❌ Invalid JSON response")
                    print(f"   Raw: {response.text[:100]}")
            else:
                print(f"   ❌ Error: {response.status_code}")
                print(f"   Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def test_fmp_stock_price():
    """Test FMP stock price API."""
    print(f"\n🔍 Testing FMP Stock Price API")
    print("-" * 50)
    
    api_key = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"
    symbol = "AAPL"
    
    url = f"https://financialmodelingprep.com/api/v3/quote/{symbol}"
    
    try:
        params = {"apikey": api_key}
        response = requests.get(url, params=params, timeout=15)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data and len(data) > 0:
                quote = data[0]
                price = quote.get("price", 0)
                print(f"✅ {symbol} Stock Price: ${price:.2f}")
            else:
                print("❌ No stock data found")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_scanner_with_real_fmp():
    """Test the options scanner with real FMP data."""
    print(f"\n🔍 Testing Options Scanner with Real FMP")
    print("-" * 50)
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from options_opportunity_scanner import OptionsOpportunityScanner
        
        scanner = OptionsOpportunityScanner()
        
        # Test FMP options data directly
        symbol = "AAPL"
        print(f"Testing FMP options for {symbol}...")
        
        fmp_data = scanner._get_fmp_options_data(symbol)
        print(f"✅ FMP returned {len(fmp_data)} options")
        
        if fmp_data:
            print(f"Sample options:")
            for i, option in enumerate(fmp_data[:3]):
                print(f"  {i+1}. {option.get('symbol', 'N/A')}")
                print(f"     Strike: ${option.get('strike', 'N/A')}")
                print(f"     Price: ${option.get('lastPrice', 'N/A')}")
        
        # Test full options chain
        print(f"\nTesting full options chain...")
        options_chain = scanner._get_options_chain(symbol)
        print(f"✅ Options chain returned {len(options_chain)} options")
        
        if options_chain:
            print(f"Sample from chain:")
            for i, option in enumerate(options_chain[:2]):
                print(f"  {i+1}. {option.get('symbol', 'N/A')} - ${option.get('lastPrice', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Scanner test failed: {e}")

if __name__ == "__main__":
    test_fmp_options()
    test_fmp_stock_price()
    test_scanner_with_real_fmp()
    
    print(f"\n🎉 FMP Real API Tests Completed!")
    print("=" * 60)
