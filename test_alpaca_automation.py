#!/usr/bin/env python3
"""
Test Alpaca Automation Integration
Verify that automation system actually places orders through Alpaca
"""
import sys
import os

# Add paths
sys.path.insert(0, os.getcwd())
sys.path.insert(0, os.path.join(os.getcwd(), 'core'))
sys.path.insert(0, os.path.join(os.getcwd(), 'trading'))

def test_alpaca_connection():
    """Test Alpaca API connection"""
    print("🔧 Testing Alpaca API Connection")
    print("-" * 35)
    
    try:
        from trading.alpaca_trading import get_paper_trader
        
        # Test paper trading connection
        trader = get_paper_trader()
        account = trader.get_account_info()
        
        if account:
            print("✅ Alpaca connection successful!")
            print(f"   Account: {account.get('account_number', 'N/A')}")
            print(f"   Equity: ${float(account.get('equity', 0)):,.2f}")
            print(f"   Buying Power: ${float(account.get('buying_power', 0)):,.2f}")
            print(f"   Paper Trading: {trader.paper_trading}")
            return True
        else:
            print("❌ Alpaca connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Alpaca connection error: {e}")
        return False

def test_automation_alpaca_integration():
    """Test automation system with Alpaca integration"""
    print("\n🤖 Testing Automation → Alpaca Integration")
    print("-" * 45)
    
    try:
        from core.automation_control import get_automation_engine
        
        # Get automation engine
        automation = get_automation_engine()
        
        print("✅ Automation engine loaded")
        
        # Test the Alpaca trade execution method
        print("\n🔍 Testing Alpaca trade execution method...")
        
        # Create test setup data
        test_setup = {
            'symbol': 'PLTR',
            'grade': 'A+',
            'confidence': 95,
            'timeframe': '15min',
            'entry_price': 25.45,
            'stop_loss': 24.82,
            'target_price': 26.98,
            'price': 25.45,
            'squeeze_release': True,
            'momentum_up': True
        }
        
        # Test the Alpaca execution method directly
        result = automation._execute_alpaca_trade(
            symbol='PLTR',
            shares=39,
            entry_price=25.45,
            stop_loss=24.82,
            take_profit=26.98
        )
        
        print(f"✅ Alpaca execution test result:")
        print(f"   Success: {result.get('success', False)}")
        print(f"   Order ID: {result.get('order_id', 'N/A')}")
        print(f"   Status: {result.get('status', 'N/A')}")
        print(f"   Message: {result.get('message', 'N/A')}")
        
        if result.get('error'):
            print(f"   Error: {result.get('error')}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ Automation integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_automation_flow():
    """Test complete automation flow with Alpaca"""
    print("\n🚀 Testing Complete Automation Flow")
    print("-" * 40)
    
    try:
        from core.automation_control import get_automation_engine
        
        automation = get_automation_engine()
        
        # Start automation in conservative mode
        print("📊 Starting automation in Conservative mode...")
        result = automation.start_automation("conservative")
        print(f"✅ Automation start result: {result}")
        
        # Create A+ setup that should trigger trade
        test_setup = {
            'symbol': 'PLTR',
            'grade': 'A+',
            'confidence': 95,
            'timeframe': '15min',
            'entry_price': 25.45,
            'stop_loss': 24.82,
            'target_price': 26.98,
            'price': 25.45,
            'squeeze_release': True,
            'momentum_up': True
        }
        
        print(f"\n📈 Testing A+ setup: PLTR Grade A+ (95%)")
        
        # Check initial trade count
        initial_trades = len(automation.executed_trades)
        print(f"   Initial trades: {initial_trades}")
        
        # Trigger automation with A+ setup
        automation._handle_new_setup(test_setup)
        
        # Check if trade was executed
        final_trades = len(automation.executed_trades)
        trades_executed = final_trades - initial_trades
        
        print(f"   Final trades: {final_trades}")
        print(f"   Trades executed: {trades_executed}")
        
        # Show trade details if executed
        if trades_executed > 0:
            trade = automation.executed_trades[-1]
            print(f"\n💰 Trade Details:")
            print(f"   Symbol: {trade['symbol']}")
            print(f"   Shares: {trade['shares']}")
            print(f"   Entry: ${trade['entry_price']:.2f}")
            print(f"   Stop: ${trade['stop_loss']:.2f}")
            print(f"   Target: ${trade['take_profit']:.2f}")
            print(f"   Alpaca Order ID: {trade.get('alpaca_order_id', 'N/A')}")
            print(f"   Alpaca Status: {trade.get('alpaca_status', 'N/A')}")
        
        # Stop automation
        automation.stop_automation()
        
        return trades_executed > 0
        
    except Exception as e:
        print(f"❌ Full automation test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🎯 ALPACA AUTOMATION INTEGRATION TEST")
    print("Testing that automation actually places orders through Alpaca")
    print("=" * 65)
    
    tests = [
        test_alpaca_connection,
        test_automation_alpaca_integration,
        test_full_automation_flow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n🎯 **TEST RESULTS: {passed}/{total} PASSED**")
    
    if passed == total:
        print("\n🎉 **ALPACA INTEGRATION WORKING!**")
        print("\n✅ **CONFIRMED:**")
        print("   • Alpaca API connection successful")
        print("   • Automation system integrated with Alpaca")
        print("   • A+ setups trigger real Alpaca orders")
        print("   • Orders placed with stop loss and take profit")
        print("   • Order IDs tracked in trade records")
        
        print("\n🚀 **YOUR AUTOMATION NOW:**")
        print("   1. Finds A+ TTM setups automatically")
        print("   2. Calculates proper position size")
        print("   3. Places REAL orders through Alpaca")
        print("   4. Sets stop loss and take profit")
        print("   5. Tracks orders with Alpaca order IDs")
        
        print("\n💎 **TO USE LIVE TRADING:**")
        print("   • Currently using PAPER TRADING (safe)")
        print("   • To enable live trading: change get_paper_trader() to get_live_trader()")
        print("   • Make sure you have sufficient funds in Alpaca account")
        print("   • Test thoroughly with paper trading first!")
        
    else:
        print(f"\n⚠️ **{total - passed} TESTS FAILED**")
        print("   • Check Alpaca credentials")
        print("   • Verify API permissions")
        print("   • Check error messages above")

if __name__ == "__main__":
    main()
