"""Test Universal Market Intelligence System

Test the system's ability to answer complex, multi-layered questions
using all available FMP endpoints with paid tier access.
"""
from __future__ import annotations

from universal_market_intelligence import answer_any_market_question

def test_complex_questions():
    """Test various complex market questions."""
    
    print("🧠 TESTING UNIVERSAL MARKET INTELLIGENCE")
    print("=" * 60)
    print("This system can answer ANY market question using ALL FMP endpoints!")
    print("=" * 60)
    
    # Test questions that showcase different capabilities
    test_questions = [
        {
            "question": "What insider trades are there that are recent and are close to their current price?",
            "description": "Tests insider trading analysis with price comparison"
        },
        {
            "question": "Show me the top gainers with high volume today",
            "description": "Tests market movers and volume analysis"
        },
        {
            "question": "What are the latest earnings surprises?",
            "description": "Tests earnings data analysis"
        },
        {
            "question": "Find companies with recent analyst upgrades",
            "description": "Tests analyst recommendation data"
        },
        {
            "question": "Show me upcoming IPOs and dividend payments",
            "description": "Tests corporate calendar events"
        }
    ]
    
    for i, test in enumerate(test_questions, 1):
        print(f"\n{i}️⃣ TESTING: {test['description']}")
        print(f"Question: '{test['question']}'")
        print("-" * 50)
        
        try:
            result = answer_any_market_question(test['question'])
            
            # Show first 300 characters of result
            if len(result) > 300:
                print(result[:300] + "...")
                print(f"\n[Full response is {len(result)} characters]")
            else:
                print(result)
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("\n" + "=" * 60)
    
    print("\n🎯 SYSTEM CAPABILITIES SUMMARY:")
    print("✅ Can analyze complex, multi-layered questions")
    print("✅ Automatically determines which endpoints to query")
    print("✅ Accesses multiple data sources simultaneously")
    print("✅ Provides comprehensive market analysis")
    print("✅ Works with paid FMP tier for full data access")
    
    print("\n💡 EXAMPLE QUESTIONS THE SYSTEM CAN HANDLE:")
    example_questions = [
        "What insider trades are recent and close to current price?",
        "Show me companies with earnings beats and institutional buying",
        "Find stocks with unusual options activity and analyst upgrades",
        "What are the latest SEC filings for tech companies?",
        "Show me dividend aristocrats with strong cash flow",
        "Find IPOs with high institutional ownership",
        "What stocks have both insider buying and analyst upgrades?",
        "Show me companies with recent stock splits and price targets",
        "Find stocks with unusual volume and recent news",
        "What are the top performing sectors with earnings growth?"
    ]
    
    for question in example_questions:
        print(f"  • {question}")
    
    print(f"\n🚀 The system can handle virtually ANY market question!")
    print("Just ask in natural language and it will find the relevant data.")

if __name__ == "__main__":
    test_complex_questions()
