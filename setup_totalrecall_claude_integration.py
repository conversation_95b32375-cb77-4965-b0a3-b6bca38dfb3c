#!/usr/bin/env python3
"""
TotalRecall + <PERSON> Integration Setup

This script sets up <PERSON> to work with your alpaca-mcp-server,
giving you natural language trading capabilities alongside your existing TotalRecall system.
"""

import os
import json
import shutil
from pathlib import Path

def setup_claude_integration():
    """Setup Claude Desktop integration."""
    print("🤖 TOTALRECALL + CLAUDE DESKTOP INTEGRATION")
    print("=" * 60)
    
    # Step 1: Check for alpaca-mcp-server
    print("\n📍 Step 1: Locating alpaca-mcp-server...")
    mcp_server_path = locate_mcp_server()
    
    if not mcp_server_path:
        print("❌ alpaca-mcp-server not found!")
        print("Please run setup_enhanced_mcp_integration.py first")
        return False
    
    print(f"✅ Found MCP server at: {mcp_server_path}")
    
    # Step 2: Get Alpaca credentials
    print("\n🔑 Step 2: Alpaca API Credentials")
    api_key, secret_key = get_alpaca_credentials()
    
    if not api_key or not secret_key:
        print("❌ Alpaca credentials required for <PERSON> integration")
        return False
    
    # Step 3: Create Claude Desktop config
    print("\n🔧 Step 3: Creating Claude Desktop Configuration...")
    config_created = create_claude_config(mcp_server_path, api_key, secret_key)
    
    if not config_created:
        print("❌ Failed to create Claude configuration")
        return False
    
    # Step 4: Setup instructions
    print("\n📚 Step 4: Setup Instructions")
    display_claude_setup_instructions()
    
    # Step 5: Create usage guide
    create_usage_guide()
    
    print("\n🎉 CLAUDE INTEGRATION SETUP COMPLETE!")
    return True

def locate_mcp_server():
    """Locate the alpaca-mcp-server."""
    # Check integrations directory first
    integrations_path = Path("integrations") / "alpaca-mcp-server"
    if integrations_path.exists() and (integrations_path / "alpaca_mcp_server.py").exists():
        return integrations_path
    
    # Check Downloads directory
    downloads_path = Path.home() / "Downloads" / "alpaca-mcp-server-main"
    if downloads_path.exists() and (downloads_path / "alpaca_mcp_server.py").exists():
        return downloads_path
    
    return None

def get_alpaca_credentials():
    """Get Alpaca API credentials."""
    # Try environment variables first
    api_key = os.getenv('ALPACA_API_KEY')
    secret_key = os.getenv('ALPACA_SECRET_KEY')
    
    if api_key and secret_key:
        print("✅ Found Alpaca credentials in environment variables")
        return api_key, secret_key
    
    # Try config file
    try:
        config_path = Path("config/config.env")
        if config_path.exists():
            with open(config_path, 'r') as f:
                content = f.read()
                if 'ALPACA_API_KEY' in content and 'ALPACA_SECRET_KEY' in content:
                    print("✅ Found Alpaca credentials in config file")
                    # Parse the config file
                    for line in content.split('\n'):
                        if line.startswith('ALPACA_API_KEY'):
                            api_key = line.split('=')[1].strip().strip('"\'')
                        elif line.startswith('ALPACA_SECRET_KEY'):
                            secret_key = line.split('=')[1].strip().strip('"\'')
                    
                    if api_key and secret_key:
                        return api_key, secret_key
    except Exception as e:
        print(f"⚠️ Could not read config file: {e}")
    
    # Use the API key from .env file that we can see
    print("\n🔑 Using Alpaca API credentials from environment...")

    # Read from .env file directly
    try:
        env_file = Path(".env")
        if env_file.exists():
            with open(env_file, 'r') as f:
                content = f.read()
                for line in content.split('\n'):
                    if line.startswith('ALPACA_API_KEY'):
                        api_key = line.split('=')[1].strip()
                    elif line.startswith('ALPACA_API_SECRET'):
                        secret_key = line.split('=')[1].strip()

                if api_key and secret_key:
                    print("✅ Found Alpaca credentials in .env file")
                    return api_key, secret_key
    except Exception as e:
        print(f"⚠️ Could not read .env file: {e}")

    # Fallback to manual entry
    print("\n🔑 Please enter your Alpaca API credentials")
    print("(Paper trading keys are recommended for testing)")

    api_key = input("\nEnter Alpaca API Key: ").strip()
    secret_key = input("Enter Alpaca Secret Key: ").strip()

    if api_key and secret_key:
        return api_key, secret_key
    else:
        return None, None

def create_claude_config(mcp_server_path, api_key, secret_key):
    """Create Claude Desktop configuration."""
    try:
        # Create Claude config
        config = {
            "mcpServers": {
                "alpaca-totalrecall": {
                    "command": "python",
                    "args": [str(mcp_server_path / "alpaca_mcp_server.py")],
                    "env": {
                        "ALPACA_API_KEY": api_key,
                        "ALPACA_SECRET_KEY": secret_key,
                        "PAPER": "true"
                    }
                }
            }
        }
        
        # Save config for user reference
        config_file = Path("integrations/claude_desktop_config.json")
        config_file.parent.mkdir(exist_ok=True)
        
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Claude Desktop config created: {config_file}")
        
        # Also create .env file for MCP server
        env_file = mcp_server_path / ".env"
        env_content = f"""ALPACA_API_KEY = "{api_key}"
ALPACA_SECRET_KEY = "{secret_key}"
PAPER = true
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print("✅ MCP server environment configured")
        return True
        
    except Exception as e:
        print(f"❌ Config creation failed: {e}")
        return False

def display_claude_setup_instructions():
    """Display Claude Desktop setup instructions."""
    print("\n📋 **CLAUDE DESKTOP SETUP INSTRUCTIONS:**")
    print("\n1️⃣ **Install Claude Desktop:**")
    print("   • Download from: https://claude.ai/download")
    print("   • Install and create account if needed")
    
    print("\n2️⃣ **Configure Claude Desktop:**")
    print("   • Open Claude Desktop")
    print("   • Go to Settings → Developer → Edit Config")
    print("   • Copy contents from: integrations/claude_desktop_config.json")
    print("   • Paste into Claude's config and save")
    
    print("\n3️⃣ **Restart Claude Desktop:**")
    print("   • Close Claude Desktop completely")
    print("   • Reopen Claude Desktop")
    print("   • You should see 'alpaca-totalrecall' server connected")
    
    print("\n4️⃣ **Test the Integration:**")
    print("   • In Claude, try: 'What's my account balance?'")
    print("   • Try: 'Show me my current positions'")
    print("   • Try: 'Get a quote for AAPL'")

def create_usage_guide():
    """Create a comprehensive usage guide."""
    guide_content = """# 🚀 TotalRecall + Claude Desktop Integration Guide

## 🎯 The Ultimate Trading Setup

You now have TWO powerful ways to interact with your trading system:

### 1. Enhanced TotalRecall Chat
**Your main trading system with enhanced capabilities**
- TTM squeeze scanning and analysis
- Real-time monitoring and alerts
- Enhanced commands for advanced features
- All your existing functionality

### 2. Claude Desktop Natural Language Trading
**Conversational AI trading companion**
- True natural language trading
- Intelligent explanations and analysis
- Advanced options strategies
- Portfolio optimization advice

## 💬 Natural Language Trading Examples

### Basic Trading
```
"What's my current account balance and buying power?"
"Show me all my current positions"
"Buy 10 shares of AAPL at market price"
"Sell 5 shares of TSLA with a limit price of $300"
"Cancel all my open orders"
```

### Advanced Options Trading
```
"Show me available option contracts for AAPL expiring next month"
"What are the Greeks for my TSLA options?"
"Place a bull call spread on NVDA using next month's options"
"Get the latest quote for AAPL250613C00200000"
"What's the implied volatility on SPY puts?"
```

### Portfolio Analysis
```
"Analyze my portfolio risk and suggest hedging strategies"
"What's my exposure to tech stocks?"
"Show me my best and worst performing positions"
"Calculate the Greeks for all my options positions"
"What's my overall portfolio beta?"
```

### Market Intelligence
```
"Show me upcoming earnings for my portfolio companies"
"What are the next dividend dates for my holdings?"
"Is the market open today?"
"Show me the market calendar for next week"
"Any corporate actions coming up for AAPL?"
```

### Watchlist Management
```
"Create a new watchlist called 'Tech Stocks' with AAPL, MSFT, GOOGL"
"Add TSLA to my Tech Stocks watchlist"
"Show me all my watchlists"
"Analyze the performance of my Growth watchlist"
```

## 🔄 Recommended Workflow

### Morning Routine:
1. **TotalRecall:** Run TTM squeeze scanner for opportunities
2. **Claude:** "Analyze today's TTM opportunities and rank them by risk/reward"
3. **TotalRecall:** Execute trades through familiar interface
4. **Claude:** "Set up alerts for my new positions"

### During Market Hours:
1. **TotalRecall:** Monitor positions and alerts
2. **Claude:** "How are my positions performing and should I adjust anything?"
3. **Both:** Execute adjustments as needed

### End of Day:
1. **Claude:** "Analyze today's trading performance and lessons learned"
2. **TotalRecall:** Review performance dashboard
3. **Claude:** "What should I watch for tomorrow?"

## 🎯 Best Practices

### Use TotalRecall For:
- ✅ TTM squeeze scanning
- ✅ Real-time monitoring
- ✅ Quick trade execution
- ✅ Performance dashboards
- ✅ Automated alerts

### Use Claude For:
- ✅ Complex analysis questions
- ✅ Strategy explanations
- ✅ Portfolio optimization
- ✅ Learning and education
- ✅ Natural language queries

## 🚀 Power User Tips

### Combine Both Systems:
```
TotalRecall: "Found A+ TTM setup on AAPL"
↓
Claude: "Analyze this AAPL setup. What's the optimal position size given my current portfolio risk?"
↓
Execute: Use either system for trade placement
↓
Monitor: TotalRecall for real-time tracking
```

### Advanced Analysis:
```
Claude: "Compare the risk/reward of buying AAPL stock vs a bull call spread"
Claude: "What's the probability of profit for my current TSLA position?"
Claude: "How would adding a protective put affect my NVDA position?"
```

## 🛡️ Safety Features

Both systems maintain all your existing safety features:
- Paper trading by default
- Risk management controls
- Position size limits
- Stop loss protections

## 🎉 You Now Have the Ultimate Trading Setup!

- **Powerful TTM Analysis** (TotalRecall)
- **Natural Language Intelligence** (Claude)
- **Seamless Integration** (Both work together)
- **Maximum Flexibility** (Use what fits the situation)

Happy Trading! 🚀
"""
    
    guide_file = Path("TOTALRECALL_CLAUDE_ULTIMATE_GUIDE.md")
    with open(guide_file, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print(f"✅ Ultimate usage guide created: {guide_file}")

def main():
    """Main setup function."""
    print("🤖 TotalRecall + Claude Desktop Ultimate Integration")
    print("=" * 60)
    print("Setting up the ultimate trading experience:")
    print("• Enhanced TotalRecall chat (already done)")
    print("• Claude Desktop natural language trading (new)")
    print("• Best of both worlds!\n")
    
    # Run Claude setup
    success = setup_claude_integration()
    
    if success:
        print("\n🎉 **ULTIMATE SETUP COMPLETE!**")
        print("\n🚀 **You Now Have:**")
        print("✅ Enhanced TotalRecall chat with advanced capabilities")
        print("✅ Claude Desktop with natural language trading")
        print("✅ Seamless integration between both systems")
        print("✅ The most powerful retail trading setup possible!")
        
        print("\n💡 **Next Steps:**")
        print("1. Install Claude Desktop: https://claude.ai/download")
        print("2. Configure Claude with the provided config")
        print("3. Test both systems")
        print("4. Read the ultimate guide for best practices")
        
        print("\n🎯 **Quick Tests:**")
        print("• TotalRecall: 'Greeks for AAPL'")
        print("• Claude: 'What are the Greeks for AAPL and explain what each one means for my trading strategy?'")
        
        print("\n📚 **Resources Created:**")
        print("• integrations/claude_desktop_config.json - Claude config")
        print("• TOTALRECALL_CLAUDE_ULTIMATE_GUIDE.md - Complete guide")
        print("• ENHANCED_MCP_INTEGRATION_README.md - Technical docs")
        
    else:
        print("\n❌ Claude setup failed")
        print("💡 Your enhanced TotalRecall system still works normally")
        print("You can try the setup again or use TotalRecall standalone")
    
    return success

if __name__ == "__main__":
    main()
