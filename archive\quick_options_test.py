#!/usr/bin/env python3
"""Quick test of the live options trading system."""

from options_strategies import get_options_strategy_recommendation

def test_live_options():
    """Test the options system with popular stocks."""
    print("🚀 Live Options Trading System Test")
    print("=" * 50)
    
    # Test popular stocks
    test_scenarios = [
        ("AAPL", "bullish", "moderate"),
        ("TSLA", "bearish", "aggressive"), 
        ("SPY", "neutral", "conservative"),
        ("QQQ", "bullish", "aggressive"),
        ("NVDA", "bullish", "moderate")
    ]
    
    for symbol, outlook, risk in test_scenarios:
        print(f"\n📊 {symbol} - {outlook.title()} outlook, {risk} risk:")
        try:
            result = get_options_strategy_recommendation(symbol, outlook, risk)
            
            print(f"   ✅ Strategy: {result['strategy']}")
            print(f"   📈 Max Profit: ${result['max_profit']}")
            print(f"   📉 Max Loss: ${result['max_loss']}")
            print(f"   ⚖️  Risk/Reward: {result['risk_reward_ratio']:.2f}")
            print(f"   🎯 Recommendation: {result['recommendation']}")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n🎉 Live system test complete!")

if __name__ == "__main__":
    test_live_options()
